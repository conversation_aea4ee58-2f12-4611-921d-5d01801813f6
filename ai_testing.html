<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .section {
        margin-bottom: 15px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 250px;
      }
      .chart-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .feature-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .ai-architecture {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      .ai-layer {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 8px;
        display: flex;
        align-items: center;
      }
      .ai-layer-icon {
        background-color: #E67E22;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 14px;
      }
      .ai-layer-content {
        flex: 1;
      }
      .ai-layer-title {
        font-weight: bold;
        margin-bottom: 2px;
        font-size: 15px;
      }
      .ai-layer-desc {
        font-size: 13px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">AI驱动的智能测试</div>
        <div class="page-number">7 / 10</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-brain"></i>AI测试应用场景
            </div>
            <div class="section-content">
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-code-branch"></i></div>
                <div>智能测试用例生成与优化</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-bug"></i></div>
                <div>缺陷预测与智能分类</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-magic"></i></div>
                <div>自愈测试与自适应测试</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-chart-pie"></i></div>
                <div>测试数据智能生成</div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-project-diagram"></i>AI测试架构
            </div>
            <div class="section-content">
              <div class="ai-architecture">
                <div class="ai-layer">
                  <div class="ai-layer-icon"><i class="fas fa-database"></i></div>
                  <div class="ai-layer-content">
                    <div class="ai-layer-title">数据层</div>
                    <div class="ai-layer-desc">测试历史数据、代码变更数据、缺陷数据</div>
                  </div>
                </div>
                <div class="ai-layer">
                  <div class="ai-layer-icon"><i class="fas fa-cogs"></i></div>
                  <div class="ai-layer-content">
                    <div class="ai-layer-title">模型层</div>
                    <div class="ai-layer-desc">机器学习模型、深度学习模型、大语言模型</div>
                  </div>
                </div>
                <div class="ai-layer">
                  <div class="ai-layer-icon"><i class="fas fa-robot"></i></div>
                  <div class="ai-layer-content">
                    <div class="ai-layer-title">智能层</div>
                    <div class="ai-layer-desc">测试生成引擎、预测引擎、自愈引擎</div>
                  </div>
                </div>
                <div class="ai-layer">
                  <div class="ai-layer-icon"><i class="fas fa-desktop"></i></div>
                  <div class="ai-layer-content">
                    <div class="ai-layer-title">应用层</div>
                    <div class="ai-layer-desc">智能测试平台、CI/CD集成、测试分析仪表板</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-line"></i>AI测试效率提升
            </div>
            <div class="chart-container">
              <div class="chart-title">AI测试与传统测试效率对比</div>
              <canvas id="efficiencyChart"></canvas>
            </div>
            <div class="section-content">
              <p>数据来源：Gartner 2024年AI测试效率研究报告</p>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-lightbulb"></i>行业案例
            </div>
            <div class="section-content">
              <div class="highlight-box">
                <strong>Microsoft:</strong> 使用AI驱动的测试生成工具，将测试覆盖率提高了35%，同时减少了40%的测试维护工作。
              </div>
              <div class="highlight-box">
                <strong>Netflix:</strong> 应用AI预测模型识别高风险代码区域，将关键缺陷检测率提高了28%。
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // AI测试效率提升图表
      const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');
      const efficiencyChart = new Chart(efficiencyCtx, {
        type: 'bar',
        data: {
          labels: ['测试用例生成', '缺陷检测', '测试维护', '回归测试', '测试分析'],
          datasets: [
            {
              label: '传统测试',
              data: [100, 100, 100, 100, 100],
              backgroundColor: 'rgba(255, 255, 255, 0.5)',
              borderColor: 'rgba(255, 255, 255, 0.8)',
              borderWidth: 1
            },
            {
              label: 'AI驱动测试',
              data: [240, 180, 150, 320, 210],
              backgroundColor: '#E67E22',
              borderColor: '#E67E22',
              borderWidth: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '效率指数（传统测试=100）',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

