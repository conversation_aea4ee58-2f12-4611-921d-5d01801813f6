<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .section {
        margin-bottom: 15px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        height: 250px;
      }
      .chart-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .feature-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .tool-comparison {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      .tool-row {
        display: flex;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        padding: 8px;
      }
      .tool-name {
        width: 100px;
        font-weight: bold;
      }
      .tool-features {
        flex: 1;
        display: flex;
        gap: 8px;
      }
      .feature-tag {
        background-color: rgba(230, 126, 34, 0.2);
        border: 1px solid #E67E22;
        border-radius: 4px;
        padding: 2px 6px;
        font-size: 13px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">自动化测试与CI/CD</div>
        <div class="page-number">6 / 10</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-robot"></i>自动化测试价值
            </div>
            <div class="section-content">
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div>提高测试效率和速度，缩短反馈周期</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-sync-alt"></i></div>
                <div>增强测试一致性和可重复性</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
                <div>提高测试覆盖率，降低人为错误</div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tools"></i>主流自动化测试工具
            </div>
            <div class="section-content">
              <div class="tool-comparison">
                <div class="tool-row">
                  <div class="tool-name">Cypress</div>
                  <div class="tool-features">
                    <div class="feature-tag">端到端测试</div>
                    <div class="feature-tag">实时重载</div>
                    <div class="feature-tag">时间旅行</div>
                  </div>
                </div>
                <div class="tool-row">
                  <div class="tool-name">Playwright</div>
                  <div class="tool-features">
                    <div class="feature-tag">多浏览器</div>
                    <div class="feature-tag">移动测试</div>
                    <div class="feature-tag">自动等待</div>
                  </div>
                </div>
                <div class="tool-row">
                  <div class="tool-name">Selenium</div>
                  <div class="tool-features">
                    <div class="feature-tag">多语言</div>
                    <div class="feature-tag">广泛支持</div>
                    <div class="feature-tag">成熟生态</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-bar"></i>自动化测试ROI分析
            </div>
            <div class="chart-container">
              <div class="chart-title">自动化测试投资回报时间线</div>
              <canvas id="roiChart"></canvas>
            </div>
            <div class="section-content">
              <p>数据来源：Sauce Labs 2023年自动化测试ROI研究</p>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-sync-alt"></i>CI/CD中的测试实践
            </div>
            <div class="chart-container">
              <div class="chart-title">CI/CD流程中的测试阶段</div>
              <canvas id="cicdChart"></canvas>
            </div>
            <div class="section-content">
              <p>CI/CD流程中的测试左移策略可以将缺陷发现时间提前75%</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 自动化测试ROI图表
      const roiCtx = document.getElementById('roiChart').getContext('2d');
      const roiChart = new Chart(roiCtx, {
        type: 'line',
        data: {
          labels: ['初始投资', '3个月', '6个月', '9个月', '12个月'],
          datasets: [
            {
              label: '手动测试成本',
              data: [0, 30, 60, 90, 120],
              borderColor: 'rgba(255, 255, 255, 0.7)',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderWidth: 2,
              pointBackgroundColor: 'rgba(255, 255, 255, 0.7)'
            },
            {
              label: '自动化测试成本',
              data: [50, 65, 75, 82, 88],
              borderColor: '#E67E22',
              backgroundColor: 'rgba(230, 126, 34, 0.2)',
              borderWidth: 2,
              pointBackgroundColor: '#E67E22'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '累计成本（相对单位）',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
      
      // CI/CD流程图
      const cicdCtx = document.getElementById('cicdChart').getContext('2d');
      const cicdChart = new Chart(cicdCtx, {
        type: 'bar',
        data: {
          labels: ['代码提交', '构建', '单元测试', '集成测试', '部署测试', '验收测试'],
          datasets: [{
            label: '测试覆盖率',
            data: [0, 10, 70, 50, 30, 40],
            backgroundColor: '#E67E22',
            borderColor: '#E67E22',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '测试覆盖率 (%)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF',
                maxRotation: 45,
                minRotation: 45
              }
            }
          },
          plugins: {
            legend: {
              display: false
            }
          }
        }
      });
    </script>
  </body>
</html>

