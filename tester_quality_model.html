<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .skill-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .skill-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .skill-content {
        flex: 1;
      }
      .skill-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .skill-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 220px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试人员素质模型</div>
        <div class="page-number">23 / 28</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-laptop-code"></i>技术能力
            </div>
            <div class="section-content">
              <div class="skill-box">
                <div class="skill-icon"><i class="fas fa-code"></i></div>
                <div class="skill-content">
                  <div class="skill-title">编程与脚本能力</div>
                  <div class="skill-desc">掌握至少一种编程语言，能够编写自动化测试脚本和工具</div>
                </div>
              </div>
              
              <div class="skill-box">
                <div class="skill-icon"><i class="fas fa-robot"></i></div>
                <div class="skill-content">
                  <div class="skill-title">自动化测试技能</div>
                  <div class="skill-desc">熟悉自动化测试框架和工具，能够设计和实现自动化测试方案</div>
                </div>
              </div>
              
              <div class="skill-box">
                <div class="skill-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div class="skill-content">
                  <div class="skill-title">性能分析能力</div>
                  <div class="skill-desc">能够设计性能测试方案，分析性能瓶颈并提出优化建议</div>
                </div>
              </div>
              
              <div class="skill-box">
                <div class="skill-icon"><i class="fas fa-database"></i></div>
                <div class="skill-content">
                  <div class="skill-title">数据库与SQL</div>
                  <div class="skill-desc">了解数据库原理，能够编写SQL查询和验证数据一致性</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-briefcase"></i>业务理解能力
            </div>
            <div class="section-content">
              <div class="skill-box">
                <div class="skill-icon"><i class="fas fa-file-alt"></i></div>
                <div class="skill-content">
                  <div class="skill-title">需求分析能力</div>
                  <div class="skill-desc">能够理解和分析业务需求，识别潜在的测试点和风险</div>
                </div>
              </div>
              
              <div class="skill-box">
                <div class="skill-icon"><i class="fas fa-industry"></i></div>
                <div class="skill-content">
                  <div class="skill-title">领域知识</div>
                  <div class="skill-desc">了解所测试系统的业务领域知识，能够从业务角度评估系统质量</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-users"></i>软技能
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">测试人员关键软技能</div>
                <canvas id="softSkillsChart" width="832" height="285" style="display: block; box-sizing: border-box; height: 190px; width: 554.667px;"></canvas>
              </div>
              
              <div class="highlight-box">
                <strong>沟通与协作：</strong>
                <p class="">测试人员需要与开发、产品、运维等多个角色协作，清晰准确的沟通能力至关重要。</p>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-lightbulb"></i>学习与创新能力
            </div>
            <div class="section-content">
              <div class="skill-box">
                <div class="skill-icon"><i class="fas fa-book"></i></div>
                <div class="skill-content">
                  <div class="skill-title">持续学习</div>
                  <div class="skill-desc">保持对新技术、新工具和新方法的学习，不断更新知识体系</div>
                </div>
              </div>
              
              <div class="skill-box">
                <div class="skill-icon"><i class="fas fa-puzzle-piece"></i></div>
                <div class="skill-content">
                  <div class="skill-title">问题解决</div>
                  <div class="skill-desc">具备分析复杂问题、定位根本原因并提出解决方案的能力</div>
                </div>
              </div>
              
              <div class="skill-box">
                <div class="skill-icon"><i class="fas fa-rocket"></i></div>
                <div class="skill-content">
                  <div class="skill-title">创新思维</div>
                  <div class="skill-desc">能够提出创新的测试方法和工具，提高测试效率和质量</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 软技能雷达图
      const softSkillsCtx = document.getElementById('softSkillsChart').getContext('2d');
      const softSkillsChart = new Chart(softSkillsCtx, {
        type: 'radar',
        data: {
          labels: ['沟通能力', '协作精神', '批判性思维', '时间管理', '抗压能力', '细节关注'],
          datasets: [{
            label: '重要性评分',
            data: [90, 85, 80, 75, 85, 95],
            backgroundColor: 'rgba(230, 126, 34, 0.2)',
            borderColor: '#E67E22',
            borderWidth: 2,
            pointBackgroundColor: '#E67E22'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              angleLines: {
                color: 'rgba(255, 255, 255, 0.2)'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.2)'
              },
              pointLabels: {
                color: '#FFFFFF',
                font: {
                  size: 12
                }
              },
              ticks: {
                backdropColor: 'transparent',
                color: 'rgba(255, 255, 255, 0.7)',
                stepSize: 20
              },
              suggestedMin: 0,
              suggestedMax: 100
            }
          },
          plugins: {
            legend: {
              display: false
            }
          }
        }
      });
    </script>
  



</body></html>