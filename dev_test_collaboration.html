<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .practice-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .practice-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .practice-content {
        flex: 1;
      }
      .practice-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .practice-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 240px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .principle-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
      }
      .principle-title {
        font-size: 16px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
      }
      .principle-title i {
        margin-right: 8px;
      }
      .principle-content {
        font-size: 14px;
        opacity: 0.9;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试与开发协作：基础理念与机制</div>
        <div class="page-number">25 / 28</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-handshake"></i>协作基础理念
            </div>
            <div class="section-content">
              <div class="principle-box">
                <div class="principle-title">
                  <i class="fas fa-users"></i>质量共同责任制
                </div>
                <div class="principle-content">
                  质量是整个团队的责任，而非测试团队独有。开发人员应参与测试过程，测试人员应理解开发挑战，共同为产品质量负责。
                </div>
              </div>
              
              <div class="principle-box">
                <div class="principle-title">
                  <i class="fas fa-bullseye"></i>统一质量标准
                </div>
                <div class="principle-content">
                  明确"完成的定义"（Definition of Done），确保开发和测试对产品质量有共同的理解和期望，避免因标准不一致导致的争议。
                </div>
              </div>
              
              <div class="principle-box">
                <div class="principle-title">
                  <i class="fas fa-balance-scale"></i>平衡速度与质量
                </div>
                <div class="principle-content">
                  在快速交付的压力下，测试与开发需要协商平衡点，既要保证产品质量，又要满足业务交付需求。
                </div>
              </div>
              
              <div class="highlight-box">
                <strong>核心价值：</strong> 建立互信、共担责任、协同作战的团队文化。
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-project-diagram"></i>敏捷协作模式
            </div>
            <div class="section-content">
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-people-arrows"></i></div>
                <div class="practice-content">
                  <div class="practice-title">跨功能团队融合</div>
                  <div class="practice-desc">测试人员作为敏捷团队的内嵌成员，与开发人员坐在一起，实时沟通协作。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-user-friends"></i></div>
                <div class="practice-content">
                  <div class="practice-title">"三人行"原则</div>
                  <div class="practice-desc">开发、测试、产品经理共同讨论需求和验收标准，确保理解一致，减少后期返工。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-code-branch"></i></div>
                <div class="practice-content">
                  <div class="practice-title">测试驱动开发支持</div>
                  <div class="practice-desc">鼓励开发人员编写单元测试，测试人员参与BDD场景编写，形成测试驱动的开发文化。</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-comments"></i>沟通协作机制
            </div>
            <div class="section-content">
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-calendar-alt"></i></div>
                <div class="practice-content">
                  <div class="practice-title">定期同步会议</div>
                  <div class="practice-desc">每日站会、迭代评审会、回顾会等，及时同步进展、风险和问题，保持信息透明。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-share-alt"></i></div>
                <div class="practice-content">
                  <div class="practice-title">文档协作共享</div>
                  <div class="practice-desc">共享测试计划、测试用例、缺陷报告，使用协作工具确保信息透明和可追溯。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-bug"></i></div>
                <div class="practice-content">
                  <div class="practice-title">缺陷协作处理</div>
                  <div class="practice-desc">缺陷不仅仅是测试的输出，更是开发和测试共同学习改进的机会，避免指责文化。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-exchange-alt"></i></div>
                <div class="practice-content">
                  <div class="practice-title">角色互换体验</div>
                  <div class="practice-desc">开发人员参与测试活动，测试人员了解开发过程，增进相互理解和同理心。</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-bar"></i>协作效益评估
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">测试与开发协作效益对比</div>
                <canvas id="collaborationBenefitChart"></canvas>
              </div>
              
              <div class="highlight-box">
                <strong>实践证明：</strong> 良好的测试与开发协作可以显著提升缺陷发现效率，降低修复成本，缩短交付周期。
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 协作效益对比图表
      const collaborationBenefitCtx = document.getElementById('collaborationBenefitChart').getContext('2d');
      const collaborationBenefitChart = new Chart(collaborationBenefitCtx, {
        type: 'bar',
        data: {
          labels: ['缺陷发现提前', '修复成本降低', '交付周期缩短', '团队满意度'],
          datasets: [
            {
              label: '传统模式',
              data: [30, 20, 10, 60],
              backgroundColor: 'rgba(231, 76, 60, 0.7)',
              borderColor: '#E74C3C',
              borderWidth: 1
            },
            {
              label: '协作模式',
              data: [80, 70, 60, 90],
              backgroundColor: 'rgba(52, 152, 219, 0.7)',
              borderColor: '#3498DB',
              borderWidth: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '改善程度(%)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

