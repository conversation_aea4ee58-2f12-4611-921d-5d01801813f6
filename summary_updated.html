<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 8px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .summary-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 6px;
      }
      .summary-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .summary-content {
        flex: 1;
      }
      .summary-title {
        font-weight: bold;
        margin-bottom: 2px;
        font-size: 15px;
      }
      .summary-desc {
        font-size: 13px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 8px;
        margin: 8px 0;
        border-radius: 0 6px 6px 0;
        font-size: 14px;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        height: 240px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .qa-box {
        background-color: rgba(52, 152, 219, 0.2);
        border-left: 4px solid #3498DB;
        padding: 8px;
        margin: 6px 0;
        border-radius: 0 6px 6px 0;
        font-size: 13px;
      }
      .qa-question {
        font-weight: bold;
        color: #3498DB;
        margin-bottom: 3px;
      }
      .qa-answer {
        opacity: 0.9;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">总结、未来展望与Q&A</div>
        <div class="page-number">28 / 28</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-check-circle"></i>核心知识体系回顾
            </div>
            <div class="section-content">
              <div class="summary-box">
                <div class="summary-icon"><i class="fas fa-foundation"></i></div>
                <div class="summary-content">
                  <div class="summary-title">测试基础理论</div>
                  <div class="summary-desc">七大测试原则、测试金字塔、测试类型分类与选择策略</div>
                </div>
              </div>
              
              <div class="summary-box">
                <div class="summary-icon"><i class="fas fa-drafting-compass"></i></div>
                <div class="summary-content">
                  <div class="summary-title">测试设计方法</div>
                  <div class="summary-desc">等价类划分、边界值分析、功能与非功能测试用例设计、测试用例管理</div>
                </div>
              </div>
              
              <div class="summary-box">
                <div class="summary-icon"><i class="fas fa-rocket"></i></div>
                <div class="summary-content">
                  <div class="summary-title">测试技术进阶</div>
                  <div class="summary-desc">DevSecOps测试左移右移、高并发大容量测试、AI测试概览与执行</div>
                </div>
              </div>
              
              <div class="summary-box">
                <div class="summary-icon"><i class="fas fa-server"></i></div>
                <div class="summary-content">
                  <div class="summary-title">测试环境与管理</div>
                  <div class="summary-desc">测试环境搭建管理、降本增效实践、环境标准化与自动化</div>
                </div>
              </div>
              
              <div class="summary-box">
                <div class="summary-icon"><i class="fas fa-network-wired"></i></div>
                <div class="summary-content">
                  <div class="summary-title">复杂系统测试</div>
                  <div class="summary-desc">整体策略、架构设计、执行流程、风险管理、质量保障体系</div>
                </div>
              </div>
              
              <div class="summary-box">
                <div class="summary-icon"><i class="fas fa-users"></i></div>
                <div class="summary-content">
                  <div class="summary-title">人员与协作</div>
                  <div class="summary-desc">测试人员素质模型、成长路径、测试与开发协作、项目交付团队协作</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-lightbulb"></i>实践应用要点
            </div>
            <div class="section-content">
              <div class="highlight-box">
                <strong>立即行动建议：</strong><br>
                1. 建立团队测试规范和标准<br>
                2. 推进测试自动化和工具平台化<br>
                3. 加强测试与开发的深度协作<br>
                4. 建立持续学习和改进机制
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-crystal-ball"></i>测试技术发展趋势
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">未来3年测试技术成熟度预测</div>
                <canvas id="trendChart"></canvas>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-question-circle"></i>常见问题解答
            </div>
            <div class="section-content">
              <div class="qa-box">
                <div class="qa-question">Q: 如何在敏捷开发中平衡测试质量和交付速度？</div>
                <div class="qa-answer">A: 通过测试左移、自动化测试、持续集成等方式，在保证质量的前提下提升效率。</div>
              </div>
              
              <div class="qa-box">
                <div class="qa-question">Q: 复杂系统测试的最大挑战是什么？</div>
                <div class="qa-answer">A: 组件间集成复杂性、故障定位困难、测试环境搭建复杂是主要挑战。</div>
              </div>
              
              <div class="qa-box">
                <div class="qa-question">Q: AI测试工具如何选择和应用？</div>
                <div class="qa-answer">A: 根据团队技术栈、测试场景和成本预算，选择合适的AI测试工具并逐步推广。</div>
              </div>
              
              <div class="qa-box">
                <div class="qa-question">Q: 测试人员如何提升自身技能？</div>
                <div class="qa-answer">A: 按照素质模型要求，从技术技能、业务理解、协作能力等维度持续学习。</div>
              </div>
              
              <div class="highlight-box">
                <strong>持续交流：</strong> 欢迎通过内部技术论坛、测试社区等渠道继续交流测试实践经验。
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 测试技术发展趋势图表
      const trendCtx = document.getElementById('trendChart').getContext('2d');
      const trendChart = new Chart(trendCtx, {
        type: 'line',
        data: {
          labels: ['2025', '2026', '2027', '2028'],
          datasets: [{
            label: 'AI驱动测试',
            data: [30, 50, 70, 85],
            backgroundColor: 'rgba(230, 126, 34, 0.2)',
            borderColor: '#E67E22',
            borderWidth: 3,
            fill: true,
            tension: 0.4
          }, {
            label: '复杂系统测试',
            data: [40, 55, 70, 80],
            backgroundColor: 'rgba(52, 152, 219, 0.2)',
            borderColor: '#3498DB',
            borderWidth: 3,
            fill: true,
            tension: 0.4
          }, {
            label: '测试自动化',
            data: [60, 70, 80, 90],
            backgroundColor: 'rgba(46, 204, 113, 0.2)',
            borderColor: '#2ECC71',
            borderWidth: 3,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '成熟度(%)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

