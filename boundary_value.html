<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .section {
        margin-bottom: 15px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 250px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .design-method {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
      }
      .method-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .method-content {
        flex: 1;
      }
      .method-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .method-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .example-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
      }
      .example-table th {
        text-align: left;
        padding: 8px;
        font-weight: bold;
        color: #E67E22;
      }
      .example-table td {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 8px;
        font-size: 14px;
      }
      .example-table tr td:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        font-weight: bold;
      }
      .example-table tr td:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试设计方法：边界值分析与其他</div>
        <div class="page-number">7 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-border-style"></i>边界值分析
            </div>
            <div class="section-content">
              <p>边界值分析是等价类划分的补充，专注于测试输入域边界附近的值，因为边界处往往容易出现缺陷。</p>
              
              <div class="highlight-box">
                <strong>边界值测试点：</strong>
                <ul>
                  <li>边界值（最小值、最大值）</li>
                  <li>边界值两侧的值（最小值-1、最小值+1、最大值-1、最大值+1）</li>
                  <li>典型值（正常范围内的代表值）</li>
                </ul>
              </div>
              
              <p><strong>示例：</strong>用户年龄输入字段（有效范围：18-60岁）</p>
              <table class="example-table">
                <thead>
                  <tr>
                    <th>测试点类型</th>
                    <th>测试值</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>最小边界值</td>
                    <td>18</td>
                  </tr>
                  <tr>
                    <td>最小边界值-1</td>
                    <td>17</td>
                  </tr>
                  <tr>
                    <td>最小边界值+1</td>
                    <td>19</td>
                  </tr>
                  <tr>
                    <td>最大边界值</td>
                    <td>60</td>
                  </tr>
                  <tr>
                    <td>最大边界值-1</td>
                    <td>59</td>
                  </tr>
                  <tr>
                    <td>最大边界值+1</td>
                    <td>61</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-table"></i>决策表测试
            </div>
            <div class="section-content">
              <p class="">决策表测试适用于测试多个条件组合的逻辑关系，确保覆盖所有可能的组合情况。</p>
              
              <div class="highlight-box">
                <strong>示例：</strong>折扣计算规则
                <ul>
                  <li>会员 + 购物满500元 = 20%折扣</li>
                  <li>会员 + 购物不满500元 = 10%折扣</li>
                  <li>非会员 + 购物满500元 = 5%折扣</li>
                  <li>非会员 + 购物不满500元 = 无折扣</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-project-diagram"></i>状态迁移测试
            </div>
            <div class="section-content">
              <p>状态迁移测试适用于具有不同状态和状态转换的系统，测试状态间的转换条件和行为。</p>
              
              <div class="highlight-box">
                <strong>适用场景：</strong>
                <ul>
                  <li>工作流系统（如订单状态：已下单→已支付→已发货→已完成）</li>
                  <li>用户会话管理（如登录状态转换）</li>
                  <li>通信协议（如TCP连接状态）</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-bug"></i>错误推测法
            </div>
            <div class="section-content">
              <p>错误推测法是基于测试人员的经验和直觉，预测系统可能出错的地方进行测试。</p>
              
              <div class="highlight-box">
                <strong>常见错误推测点：</strong>
                <ul>
                  <li>空值、null值处理</li>
                  <li class="">特殊字符输入（如SQL注入字符）</li>
                  <li>极端大小的数据</li>
                  <li class="">并发操作和竞态条件</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
  



</body></html>