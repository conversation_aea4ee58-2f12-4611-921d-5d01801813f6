<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .section {
        margin-bottom: 15px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 250px;
      }
      .chart-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .key-point {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .key-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .trend-item {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 8px;
        margin-bottom: 8px;
        display: flex;
        align-items: flex-start;
      }
      .trend-icon {
        background-color: #E67E22;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        flex-shrink: 0;
      }
      .trend-content {
        flex: 1;
      }
      .trend-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .trend-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .quote {
        font-style: italic;
        padding: 10px;
        border-left: 3px solid #E67E22;
        margin: 10px 0;
        background-color: rgba(255, 255, 255, 0.05);
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">总结与展望</div>
        <div class="page-number">10 / 10</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-check-circle"></i>关键点回顾
            </div>
            <div class="section-content">
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-bullseye"></i></div>
                <div>测试是质量保障的关键环节，不仅发现缺陷，更是提升用户体验</div>
              </div>
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-book"></i></div>
                <div>七大测试原则指导测试实践，测试金字塔指导资源分配</div>
              </div>
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-tasks"></i></div>
                <div>功能测试与非功能测试相辅相成，黑盒、白盒、灰盒各有所长</div>
              </div>
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-sync-alt"></i></div>
                <div>测试生命周期五阶段确保测试活动系统化、规范化</div>
              </div>
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-robot"></i></div>
                <div>自动化测试提高效率，AI驱动测试引领未来</div>
              </div>
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-arrows-alt-h"></i></div>
                <div>测试左移右移策略优化测试资源配置，提高测试效益</div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-quote-right"></i>测试团队使命
            </div>
            <div class="section-content">
              <div class="quote">
                "测试团队不仅是质量的守门人，更是用户体验的代言人，产品成功的推动者。"
              </div>
              <p>测试不是目的，而是手段。最终目标是交付高质量的产品，提升用户满意度，为企业创造价值。</p>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-line"></i>测试趋势展望
            </div>
            <div class="section-content">
              <div class="trend-item">
                <div class="trend-icon"><i class="fas fa-brain"></i></div>
                <div class="trend-content">
                  <div class="trend-title">AI驱动测试全面普及</div>
                  <div class="trend-desc">大语言模型自动生成测试用例，智能预测缺陷，自适应测试策略</div>
                </div>
              </div>
              
              <div class="trend-item">
                <div class="trend-icon"><i class="fas fa-vr-cardboard"></i></div>
                <div class="trend-content">
                  <div class="trend-title">沉浸式体验测试</div>
                  <div class="trend-desc">AR/VR/MR应用测试方法论成熟，情感和体验测试成为标准</div>
                </div>
              </div>
              
              <div class="trend-item">
                <div class="trend-icon"><i class="fas fa-microchip"></i></div>
                <div class="trend-content">
                  <div class="trend-title">量子计算测试</div>
                  <div class="trend-desc">量子算法验证和量子安全测试成为新兴领域</div>
                </div>
              </div>
              
              <div class="trend-item">
                <div class="trend-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="trend-content">
                  <div class="trend-title">安全测试左移</div>
                  <div class="trend-desc">安全测试与功能测试深度融合，成为开发流程必选项</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-graduation-cap"></i>持续学习资源
            </div>
            <div class="section-content">
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-certificate"></i></div>
                <div>ISTQB认证 - 国际软件测试资格认证</div>
              </div>
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-book"></i></div>
                <div>《Agile Testing》Lisa Crispin & Janet Gregory</div>
              </div>
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-globe"></i></div>
                <div>Ministry of Testing - 测试社区与资源</div>
              </div>
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-video"></i></div>
                <div>TestAutomationU - 免费自动化测试课程</div>
              </div>
              <div class="key-point">
                <div class="key-icon"><i class="fas fa-podcast"></i></div>
                <div>TestGuild Podcast - 测试专家访谈</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
  </body>
</html>

