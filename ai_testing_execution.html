<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .ai-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .ai-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .ai-content {
        flex: 1;
      }
      .ai-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .ai-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 200px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .example-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
      }
      .example-title {
        font-size: 16px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
      }
      .example-title i {
        margin-right: 8px;
      }
      .example-content {
        font-size: 14px;
        opacity: 0.9;
      }
      .example-result {
        font-size: 14px;
        font-style: italic;
        margin-top: 5px;
        color: #3498DB;
      }
      .tool-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
      }
      .tool-icon {
        font-size: 24px;
        color: #E67E22;
        margin-right: 15px;
      }
      .tool-content {
        flex: 1;
      }
      .tool-title {
        font-weight: bold;
        margin-bottom: 2px;
      }
      .tool-desc {
        font-size: 14px;
        opacity: 0.9;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">AI与智能测试：高效执行与评估</div>
        <div class="page-number">14 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-play-circle"></i>AI在测试执行中的应用
            </div>
            <div class="section-content">
              <div class="example-box">
                <div class="example-title">
                  <i class="fas fa-sync-alt"></i>智能回归测试
                </div>
                <div class="example-content">
                  <strong>实例：</strong> Facebook使用AI驱动的智能回归测试系统Sapienz，自动探索应用并识别崩溃。
                </div>
                <div class="example-result">
                  结果：每天自动发现超过100个潜在问题，减少75%的回归测试时间
                </div>
              </div>
              
              <div class="ai-box">
                <div class="ai-icon"><i class="fas fa-robot"></i></div>
                <div class="ai-content">
                  <div class="ai-title">自愈合测试</div>
                  <div class="ai-desc">AI自动修复因UI变化导致的测试脚本失败，减少维护成本</div>
                </div>
              </div>
              
              <div class="ai-box">
                <div class="ai-icon"><i class="fas fa-eye"></i></div>
                <div class="ai-content">
                  <div class="ai-title">视觉验证</div>
                  <div class="ai-desc">使用计算机视觉技术自动检测UI变化和视觉缺陷</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-database"></i>AI在测试资产管理中的应用
            </div>
            <div class="section-content">
              <div class="example-box">
                <div class="example-title">
                  <i class="fas fa-table"></i>智能测试数据生成
                </div>
                <div class="example-content">
                  <strong>实例：</strong> IBM使用生成式AI创建符合业务规则的测试数据，同时保护敏感信息。
                </div>
                <div class="example-result">
                  结果：测试数据准备时间减少80%，数据质量提升40%
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-line"></i>AI在测试质量评估中的应用
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">AI质量预测准确率</div>
                <canvas id="qualityPredictionChart" width="832" height="255" style="display: block; box-sizing: border-box; height: 170px; width: 554.667px;"></canvas>
              </div>
              
              <div class="ai-box">
                <div class="ai-icon"><i class="fas fa-bug"></i></div>
                <div class="ai-content">
                  <div class="ai-title">缺陷根因分析</div>
                  <div class="ai-desc">AI分析缺陷模式，自动识别根本原因和相关代码区域</div>
                </div>
              </div>
              
              <div class="ai-box">
                <div class="ai-icon"><i class="fas fa-chart-bar"></i></div>
                <div class="ai-content">
                  <div class="ai-title">质量预测</div>
                  <div class="ai-desc">基于历史数据预测软件质量和潜在风险区域</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tools"></i>AI测试工具与平台
            </div>
            <div class="section-content">
              <div class="tool-box">
                <div class="tool-icon"><i class="fas fa-eye"></i></div>
                <div class="tool-content">
                  <div class="tool-title">Applitools</div>
                  <div class="tool-desc">基于AI的视觉测试平台，自动检测UI变化和视觉缺陷</div>
                </div>
              </div>
              
              <div class="tool-box">
                <div class="tool-icon"><i class="fas fa-magic"></i></div>
                <div class="tool-content">
                  <div class="tool-title">Mabl</div>
                  <div class="tool-desc">智能端到端测试平台，自动创建和维护测试脚本</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // AI质量预测准确率图表
      const qualityPredictionCtx = document.getElementById('qualityPredictionChart').getContext('2d');
      const qualityPredictionChart = new Chart(qualityPredictionCtx, {
        type: 'line',
        data: {
          labels: ['2020', '2021', '2022', '2023', '2024', '2025'],
          datasets: [
            {
              label: '缺陷预测准确率',
              data: [65, 70, 78, 85, 89, 92],
              borderColor: '#E67E22',
              backgroundColor: 'rgba(230, 126, 34, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.1
            },
            {
              label: '质量风险预测准确率',
              data: [60, 68, 75, 80, 85, 88],
              borderColor: '#3498DB',
              backgroundColor: 'rgba(52, 152, 219, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '准确率(%)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  



</body></html>