<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 8px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .layer-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 8px;
        margin-bottom: 6px;
        border-left: 4px solid #E67E22;
      }
      .layer-title {
        font-size: 16px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 3px;
        display: flex;
        align-items: center;
      }
      .layer-title i {
        margin-right: 8px;
      }
      .layer-content {
        font-size: 13px;
        opacity: 0.9;
      }
      .design-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 5px;
      }
      .design-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .design-content {
        flex: 1;
      }
      .design-title {
        font-weight: bold;
        margin-bottom: 2px;
      }
      .design-desc {
        font-size: 13px;
        opacity: 0.9;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        height: 300px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">复杂系统测试架构设计</div>
        <div class="page-number">18 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-layer-group"></i>分层测试架构模型
            </div>
            <div class="section-content">
              <div class="layer-box">
                <div class="layer-title">
                  <i class="fas fa-desktop"></i>业务应用层测试
                </div>
                <div class="layer-content">
                  端到端业务流程测试、用户界面测试、业务逻辑验证，确保定制软件满足业务需求。
                </div>
              </div>
              
              <div class="layer-box">
                <div class="layer-title">
                  <i class="fas fa-cogs"></i>中间件服务层测试
                </div>
                <div class="layer-content">
                  数据库、消息队列、缓存等通用软件的集成测试，验证服务间的协调配合。
                </div>
              </div>
              
              <div class="layer-box">
                <div class="layer-title">
                  <i class="fas fa-server"></i>操作系统层测试
                </div>
                <div class="layer-content">
                  操作系统配置、资源管理、进程调度等基础功能测试，确保系统稳定运行。
                </div>
              </div>
              
              <div class="layer-box">
                <div class="layer-title">
                  <i class="fas fa-microchip"></i>硬件抽象层测试
                </div>
                <div class="layer-content">
                  驱动程序、硬件接口、设备控制等底层功能测试，验证软硬件接口正确性。
                </div>
              </div>
              
              <div class="layer-box">
                <div class="layer-title">
                  <i class="fas fa-memory"></i>硬件平台层测试
                </div>
                <div class="layer-content">
                  通用硬件和专用硬件的功能、性能、兼容性测试，确保硬件平台可靠性。
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-sitemap"></i>测试架构设计原则
            </div>
            <div class="section-content">
              <div class="design-box">
                <div class="design-icon"><i class="fas fa-puzzle-piece"></i></div>
                <div class="design-content">
                  <div class="design-title">模块化测试设计</div>
                  <div class="design-desc">将复杂系统分解为独立的测试模块，每个模块有明确的测试边界和接口定义。</div>
                </div>
              </div>
              
              <div class="design-box">
                <div class="design-icon"><i class="fas fa-link"></i></div>
                <div class="design-content">
                  <div class="design-title">接口契约验证</div>
                  <div class="design-desc">定义清晰的接口契约，通过契约测试确保组件间交互的正确性和一致性。</div>
                </div>
              </div>
              
              <div class="design-box">
                <div class="design-icon"><i class="fas fa-sync-alt"></i></div>
                <div class="design-content">
                  <div class="design-title">增量集成策略</div>
                  <div class="design-desc">采用自底向上或自顶向下的增量集成方式，逐步构建完整的系统测试环境。</div>
                </div>
              </div>
              
              <div class="design-box">
                <div class="design-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="design-content">
                  <div class="design-title">故障隔离机制</div>
                  <div class="design-desc">设计故障隔离和恢复机制，确保单个组件故障不会影响整个测试环境。</div>
                </div>
              </div>
              
              <div class="design-box">
                <div class="design-icon"><i class="fas fa-eye"></i></div>
                <div class="design-content">
                  <div class="design-title">可观测性设计</div>
                  <div class="design-desc">内置监控和日志机制，提供系统运行状态的全面可见性，便于问题诊断。</div>
                </div>
              </div>
              
              <div class="design-box">
                <div class="design-icon"><i class="fas fa-expand-arrows-alt"></i></div>
                <div class="design-content">
                  <div class="design-title">可扩展性考虑</div>
                  <div class="design-desc">设计灵活的测试架构，支持新组件的快速集成和测试用例的动态扩展。</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-area"></i>测试架构复杂度分析
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">测试架构层次与复杂度关系</div>
                <canvas id="architectureChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 测试架构复杂度分析图表
      const architectureCtx = document.getElementById('architectureChart').getContext('2d');
      const architectureChart = new Chart(architectureCtx, {
        type: 'line',
        data: {
          labels: ['硬件层', '驱动层', '操作系统层', '中间件层', '应用层', '集成层'],
          datasets: [{
            label: '测试复杂度',
            data: [60, 70, 80, 90, 85, 95],
            backgroundColor: 'rgba(230, 126, 34, 0.2)',
            borderColor: '#E67E22',
            borderWidth: 3,
            fill: true,
            tension: 0.4
          }, {
            label: '集成难度',
            data: [40, 50, 65, 85, 80, 100],
            backgroundColor: 'rgba(52, 152, 219, 0.2)',
            borderColor: '#3498DB',
            borderWidth: 3,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '复杂度指数',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

