<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .section {
        margin-bottom: 15px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 250px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .design-method {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
      }
      .method-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .method-content {
        flex: 1;
      }
      .method-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .method-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .example-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
      }
      .example-table th {
        text-align: left;
        padding: 8px;
        font-weight: bold;
        color: #E67E22;
      }
      .example-table td {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 8px;
        font-size: 14px;
      }
      .example-table tr td:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        font-weight: bold;
      }
      .example-table tr td:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试设计方法与用例实践</div>
        <div class="page-number">5 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-sitemap"></i>测试设计方法
            </div>
            <div class="section-content">
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-th-large"></i></div>
                <div class="method-content">
                  <div class="method-title">等价类划分</div>
                  <div class="method-desc">将输入域划分为有效和无效等价类，每类选择一个代表值</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-border-style"></i></div>
                <div class="method-content">
                  <div class="method-title">边界值分析</div>
                  <div class="method-desc">测试边界值及其两侧的值，如最小值、最小值-1、最小值+1</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-table"></i></div>
                <div class="method-content">
                  <div class="method-title">决策表测试</div>
                  <div class="method-desc">针对多条件组合的逻辑关系，确保覆盖所有组合</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-project-diagram"></i></div>
                <div class="method-content">
                  <div class="method-title">状态迁移测试</div>
                  <div class="method-desc">测试系统在不同状态间的转换条件和行为</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-bug"></i></div>
                <div class="method-content">
                  <div class="method-title">错误推测法</div>
                  <div class="method-desc">基于经验预测系统可能出错的地方进行测试</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="chart-container">
              <div class="chart-title">测试设计方法使用频率</div>
              <canvas id="methodUsageChart" width="832" height="330" style="display: block; box-sizing: border-box; height: 220px; width: 554.667px;"></canvas>
            </div>
            <div class="section-content">
              <p>数据来源：ISTQB全球测试实践调查 2023</p>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-file-alt"></i>测试用例设计示例
            </div>
            <div class="section-content">
              <div class="highlight-box">
                <strong>登录功能测试用例示例（等价类划分）</strong>
                <table class="example-table mt-2">
                  <tbody><tr>
                    <td>用例ID</td>
                    <td>TC-001</td>
                  </tr>
                  <tr>
                    <td>前置条件</td>
                    <td>用户已注册</td>
                  </tr>
                  <tr>
                    <td>测试数据</td>
                    <td>有效用户名: <EMAIL><br>有效密码: Password123</td>
                  </tr>
                  <tr>
                    <td>步骤</td>
                    <td>1. 输入有效用户名<br>2. 输入有效密码<br>3. 点击登录按钮</td>
                  </tr>
                  <tr>
                    <td>预期结果</td>
                    <td>登录成功，跳转到首页</td>
                  </tr>
                </tbody></table>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-code-branch"></i>测试用例管理
            </div>
            <div class="section-content">
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-layer-group"></i></div>
                <div class="method-content">
                  <div class="method-title">测试用例组织</div>
                  <div class="method-desc">按功能模块、测试类型或用户场景进行分类</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-sync-alt"></i></div>
                <div class="method-content">
                  <div class="method-title">测试用例维护</div>
                  <div class="method-desc">定期评审、更新和优化测试用例集</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-chart-line"></i></div>
                <div class="method-content">
                  <div class="method-title">测试覆盖率分析</div>
                  <div class="method-desc">确保测试用例覆盖所有功能点和需求</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-tools"></i></div>
                <div class="method-content">
                  <div class="method-title">测试用例工具</div>
                  <div class="method-desc">TestRail, Zephyr, Xray, qTest等专业工具</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 测试设计方法使用频率图表
      const methodCtx = document.getElementById('methodUsageChart').getContext('2d');
      const methodUsageChart = new Chart(methodCtx, {
        type: 'bar',
        data: {
          labels: ['等价类划分', '边界值分析', '决策表测试', '状态迁移测试', '错误推测法'],
          datasets: [{
            label: '使用频率',
            data: [85, 78, 62, 55, 45],
            backgroundColor: '#E67E22'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '使用频率 (%)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              display: false
            }
          }
        }
      });
    </script>
  



</body></html>