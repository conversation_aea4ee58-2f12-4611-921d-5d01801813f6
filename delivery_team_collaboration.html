<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .practice-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .practice-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .practice-content {
        flex: 1;
      }
      .practice-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .practice-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 280px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .phase-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
      }
      .phase-title {
        font-size: 16px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
      }
      .phase-title i {
        margin-right: 8px;
      }
      .phase-content {
        font-size: 14px;
        opacity: 0.9;
      }
      .improvement-box {
        background-color: rgba(52, 152, 219, 0.2);
        border-left: 4px solid #3498DB;
        padding: 6px;
        margin: 4px 0;
        border-radius: 0 6px 6px 0;
        font-size: 13px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试团队与市场项目交付团队协作</div>
        <div class="page-number">27 / 28</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-building"></i>市场项目交付特点与挑战
            </div>
            <div class="section-content">
              <div class="phase-box">
                <div class="phase-title">
                  <i class="fas fa-user-tie"></i>客户定制化需求
                </div>
                <div class="phase-content">
                  每个客户的业务场景和需求都不同，需要针对客户特定环境进行适配，客户验收标准可能与标准产品不同。
                </div>
              </div>
              
              <div class="phase-box">
                <div class="phase-title">
                  <i class="fas fa-server"></i>独立部署环境
                </div>
                <div class="phase-content">
                  客户现场或专用云环境部署，网络环境、安全策略各不相同，硬件配置和性能要求差异化。
                </div>
              </div>
              
              <div class="phase-box">
                <div class="phase-title">
                  <i class="fas fa-clock"></i>交付时间压力
                </div>
                <div class="phase-content">
                  客户合同约定的严格交付时间，项目延期可能面临商务风险，需要在质量和进度间找到平衡。
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-handshake"></i>协作机制与最佳实践
            </div>
            <div class="section-content">
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-clipboard-list"></i></div>
                <div class="practice-content">
                  <div class="practice-title">项目启动阶段深度参与</div>
                  <div class="practice-desc">参与客户需求调研，理解客户业务场景，制定针对性测试策略。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-cogs"></i></div>
                <div class="practice-content">
                  <div class="practice-title">环境适配测试支持</div>
                  <div class="practice-desc">协助交付团队进行客户环境适配测试，验证系统在客户特定环境下的表现。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-users"></i></div>
                <div class="practice-content">
                  <div class="practice-title">客户验收测试协助</div>
                  <div class="practice-desc">协助制定客户验收测试计划，现场支持客户验收测试执行。</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-line"></i>测试工作改善策略
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">市场项目测试效率提升路径</div>
                <canvas id="improvementChart"></canvas>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-lightbulb"></i>测试团队自身改善实践
            </div>
            <div class="section-content">
              <div class="improvement-box">
                <strong>标准化测试资产：</strong> 建立可复用的测试用例库、测试数据模板和自动化测试脚本，减少重复工作。
              </div>
              <div class="improvement-box">
                <strong>环境快速复制：</strong> 建立标准化的测试环境模板，支持快速复制和部署，适应不同客户的环境需求。
              </div>
              <div class="improvement-box">
                <strong>知识库建设：</strong> 积累客户项目经验，建立问题知识库和最佳实践库，提升团队整体能力。
              </div>
              <div class="improvement-box">
                <strong>工具平台化：</strong> 开发测试工具平台，支持多项目并行测试，提供统一的测试管理和报告能力。
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 市场项目测试效率提升路径图
      const improvementCtx = document.getElementById('improvementChart').getContext('2d');
      const improvementChart = new Chart(improvementCtx, {
        type: 'line',
        data: {
          labels: ['传统模式', '标准化阶段', '自动化阶段', '平台化阶段', '智能化阶段'],
          datasets: [{
            label: '测试效率',
            data: [100, 130, 180, 250, 320],
            backgroundColor: 'rgba(230, 126, 34, 0.2)',
            borderColor: '#E67E22',
            borderWidth: 3,
            pointBackgroundColor: '#E67E22',
            pointRadius: 6,
            tension: 0.4
          }, {
            label: '交付质量',
            data: [100, 115, 140, 170, 200],
            backgroundColor: 'rgba(52, 152, 219, 0.2)',
            borderColor: '#3498DB',
            borderWidth: 3,
            pointBackgroundColor: '#3498DB',
            pointRadius: 6,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              }
            },
            x: {
              ticks: {
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

