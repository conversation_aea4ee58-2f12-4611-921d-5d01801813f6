<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .section {
        margin-bottom: 15px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 250px; /* Adjusted height */
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .test-type {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .test-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .test-content {
        flex: 1;
      }
      .test-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .test-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .method-comparison {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
      }
      .method-comparison th {
        text-align: left;
        padding: 8px;
        font-weight: bold;
        color: #E67E22;
      }
      .method-comparison td {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 8px;
      }
      .method-comparison tr td:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        font-weight: bold;
      }
      .method-comparison tr td:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试技术精粹：类型与方法</div>
        <div class="page-number">4 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tasks"></i>测试类型
            </div>
            <div class="section-content">
              <div class="test-type">
                <div class="test-icon"><i class="fas fa-check-circle"></i></div>
                <div class="test-content">
                  <div class="test-title">功能测试</div>
                  <div class="test-desc">验证软件是否按照需求规格说明书的要求正确执行功能</div>
                </div>
              </div>
              
              <div class="test-type">
                <div class="test-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div class="test-content">
                  <div class="test-title">非功能测试</div>
                  <div class="test-desc">评估系统的性能、可用性、安全性等质量特性</div>
                </div>
              </div>
              
              <div class="test-type">
                <div class="test-icon"><i class="fas fa-bomb"></i></div>
                <div class="test-content">
                  <div class="test-title">混沌测试</div>
                  <div class="test-desc">通过引入故障来验证系统的弹性和容错能力</div>
                </div>
              </div>
              
              <div class="test-type">
                <div class="test-icon"><i class="fas fa-globe"></i></div>
                <div class="test-content">
                  <div class="test-title">国际化测试</div>
                  <div class="test-desc">验证软件在不同语言和文化环境下的适用性</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="chart-container">
              <div class="chart-title">企业测试类型分布</div>
              <canvas id="testTypeChart" width="832" height="330" style="display: block; box-sizing: border-box; height: 220px; width: 554.667px;"></canvas>
            </div>
            <div class="section-content">
              <p>数据来源：World Quality Report 2023-2024</p>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-microscope"></i>测试方法对比
            </div>
            <div class="section-content">
              <table class="method-comparison">
                <thead>
                  <tr>
                    <th>方法</th>
                    <th>关注点</th>
                    <th>适用场景</th>
                    <th>优势</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>黑盒测试</td>
                    <td>功能行为</td>
                    <td>验收测试、系统测试</td>
                    <td>不需要了解内部实现</td>
                  </tr>
                  <tr>
                    <td>白盒测试</td>
                    <td>内部结构</td>
                    <td>单元测试、集成测试</td>
                    <td>高代码覆盖率</td>
                  </tr>
                  <tr>
                    <td>灰盒测试</td>
                    <td>部分内部结构</td>
                    <td>API测试、数据库测试</td>
                    <td>结合两种方法的优点</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tools"></i>测试工具推荐
            </div>
            <div class="section-content">
              <div class="test-type">
                <div class="test-icon"><i class="fas fa-check-circle"></i></div>
                <div class="test-content">
                  <div class="test-title">功能测试</div>
                  <div class="test-desc">Selenium, Cypress, TestComplete</div>
                </div>
              </div>
              
              <div class="test-type">
                <div class="test-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div class="test-content">
                  <div class="test-title">性能测试</div>
                  <div class="test-desc">JMeter, LoadRunner, k6</div>
                </div>
              </div>
              
              <div class="test-type">
                <div class="test-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="test-content">
                  <div class="test-title">安全测试</div>
                  <div class="test-desc">OWASP ZAP, Burp Suite, Acunetix</div>
                </div>
              </div>
              
              <div class="test-type">
                <div class="test-icon"><i class="fas fa-bomb"></i></div>
                <div class="test-content">
                  <div class="test-title">混沌测试</div>
                  <div class="test-desc">Chaos Monkey, Gremlin, Litmus</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 测试类型分布图表
      const typeCtx = document.getElementById('testTypeChart').getContext('2d');
      const testTypeChart = new Chart(typeCtx, {
        type: 'radar',
        data: {
          labels: ['功能测试', '性能测试', '安全测试', '可用性测试', '兼容性测试', '混沌测试', '国际化测试'],
          datasets: [{
            label: '企业采用率',
            data: [95, 75, 65, 55, 50, 30, 45],
            backgroundColor: 'rgba(230, 126, 34, 0.2)',
            borderColor: '#E67E22',
            borderWidth: 2,
            pointBackgroundColor: '#E67E22'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              beginAtZero: true,
              max: 100,
              ticks: {
                display: false
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.2)'
              },
              angleLines: {
                color: 'rgba(255, 255, 255, 0.2)'
              },
              pointLabels: {
                color: '#FFFFFF',
                font: {
                  size: 10 /* Reduced font size for labels */
                }
              }
            }
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return `采用率: ${context.parsed.r}%`;
                }
              }
            }
          },
          layout: {
            padding: {
              left: 0, 
              right: 0,
              top: 0,
              bottom: 0
            }
          }
        }
      });
    </script>
  



</body></html>