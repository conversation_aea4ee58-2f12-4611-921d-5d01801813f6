<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .section {
        margin-bottom: 15px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 250px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .design-method {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
      }
      .method-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .method-content {
        flex: 1;
      }
      .method-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .method-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .example-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
      }
      .example-table th {
        text-align: left;
        padding: 8px;
        font-weight: bold;
        color: #E67E22;
      }
      .example-table td {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 8px;
        font-size: 14px;
      }
      .example-table tr td:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        font-weight: bold;
      }
      .example-table tr td:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试设计方法：等价类划分</div>
        <div class="page-number">6 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-th-large"></i>等价类划分定义与原则
            </div>
            <div class="section-content">
              <p>等价类划分是一种将测试输入域划分为多个子集（等价类）的黑盒测试技术，每个子集中的元素在测试中被视为等效的。</p>
              
              <div class="highlight-box">
                <strong>基本原则：</strong>
                <ul>
                  <li>每个等价类代表一组具有相同行为或处理方式的输入</li>
                  <li>从每个等价类中选择一个代表值进行测试</li>
                  <li>如果一个等价类的代表值发现了缺陷，同一等价类中的其他值也可能发现相同缺陷</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-layer-group"></i>等价类划分的子类
            </div>
            <div class="section-content">
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-sign-in-alt"></i></div>
                <div class="method-content">
                  <div class="method-title">输入域等价类</div>
                  <div class="method-desc">基于输入参数的取值范围划分，如数值范围、字符类型等</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-sign-out-alt"></i></div>
                <div class="method-content">
                  <div class="method-title">输出域等价类</div>
                  <div class="method-desc">基于系统输出结果的不同类型划分，如成功/失败/异常状态</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-code-branch"></i></div>
                <div class="method-content">
                  <div class="method-title">条件等价类</div>
                  <div class="method-desc">基于条件语句的不同分支划分，如if-else条件的不同路径</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-random"></i></div>
                <div class="method-content">
                  <div class="method-title">状态等价类</div>
                  <div class="method-desc">基于系统状态的不同类型划分，如登录/未登录状态</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-check-circle"></i>有效等价类与无效等价类
            </div>
            <div class="section-content">
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-check"></i></div>
                <div class="method-content">
                  <div class="method-title">有效等价类</div>
                  <div class="method-desc">满足规格说明的有效输入值集合，系统应正常处理</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-times"></i></div>
                <div class="method-content">
                  <div class="method-title">无效等价类</div>
                  <div class="method-desc">不满足规格说明的无效输入值集合，系统应拒绝或给出错误提示</div>
                </div>
              </div>
              
              <div class="highlight-box">
                <strong>重要性：</strong>无效等价类测试对发现系统对异常输入的处理能力至关重要，可防止系统崩溃或安全漏洞。
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-laptop-code"></i>等价类划分示例
            </div>
            <div class="section-content">
              <p><strong>场景：</strong>用户年龄输入字段（有效范围：18-60岁）</p>
              
              <table class="example-table">
                <thead>
                  <tr>
                    <th>等价类类型</th>
                    <th>等价类描述</th>
                    <th>测试值</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>有效等价类</td>
                    <td>有效年龄范围</td>
                    <td>30</td>
                  </tr>
                  <tr>
                    <td>无效等价类</td>
                    <td>小于最小年龄</td>
                    <td>15</td>
                  </tr>
                  <tr>
                    <td>无效等价类</td>
                    <td>大于最大年龄</td>
                    <td>65</td>
                  </tr>
                  <tr>
                    <td>无效等价类</td>
                    <td>非数字输入</td>
                    <td>"abc"</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
  



</body></html>