<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .design-method {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .method-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .method-content {
        flex: 1;
      }
      .method-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .method-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 220px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .tool-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
      }
      .tool-icon {
        font-size: 24px;
        color: #E67E22;
        margin-right: 15px;
      }
      .tool-content {
        flex: 1;
      }
      .tool-name {
        font-weight: bold;
        margin-bottom: 2px;
      }
      .tool-desc {
        font-size: 14px;
        opacity: 0.9;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">高并发与大容量测试设计</div>
        <div class="page-number">12 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-bolt"></i>高并发测试的挑战与目标
            </div>
            <div class="section-content">
              <p>高并发测试旨在验证系统在大量用户同时访问时的性能表现和稳定性。</p>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-bullseye"></i></div>
                <div class="method-content">
                  <div class="method-title">响应时间</div>
                  <div class="method-desc">在高并发下，系统响应时间是否仍在可接受范围内</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-server"></i></div>
                <div class="method-content">
                  <div class="method-title">系统稳定性</div>
                  <div class="method-desc">系统是否能在持续高负载下稳定运行，无崩溃或资源耗尽</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div class="method-content">
                  <div class="method-title">吞吐量</div>
                  <div class="method-desc">系统在单位时间内能处理的最大事务数或请求数</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-exclamation-triangle"></i></div>
                <div class="method-content">
                  <div class="method-title">瓶颈识别</div>
                  <div class="method-desc">发现系统在高并发下的性能瓶颈，如CPU、内存、网络等</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-database"></i>大容量测试的设计策略
            </div>
            <div class="section-content">
              <p>大容量测试验证系统处理大量数据的能力，关注数据库性能、存储系统和数据处理效率。</p>
              
              <div class="highlight-box">
                <strong>大容量测试关键点：</strong>
                <ul>
                  <li>数据库性能：索引效率、查询优化、连接池配置</li>
                  <li>存储系统：I/O性能、文件系统效率、存储容量</li>
                  <li>数据处理：批处理能力、缓存策略、数据压缩</li>
                  <li>扩展性：水平扩展能力、垂直扩展能力</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-project-diagram"></i>负载模型与场景设计
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">典型负载模型</div>
                <canvas id="loadModelChart" width="832" height="285" style="display: block; box-sizing: border-box; height: 190px; width: 554.667px;"></canvas>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-users"></i></div>
                <div class="method-content">
                  <div class="method-title">用户行为建模</div>
                  <div class="method-desc">基于真实用户行为模式设计测试场景，包括思考时间和操作序列</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-chart-line"></i></div>
                <div class="method-content">
                  <div class="method-title">阶梯式增长</div>
                  <div class="method-desc">逐步增加负载，观察系统在不同负载级别的表现</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tools"></i>性能测试工具与平台
            </div>
            <div class="section-content">
              <div class="tool-box">
                <div class="tool-icon"><i class="fas fa-weight-hanging"></i></div>
                <div class="tool-content">
                  <div class="tool-name">JMeter</div>
                  <div class="tool-desc">开源性能测试工具，支持多种协议和分布式测试</div>
                </div>
              </div>
              
              <div class="tool-box">
                <div class="tool-icon"><i class="fas fa-rocket"></i></div>
                <div class="tool-content">
                  <div class="tool-name">LoadRunner</div>
                  <div class="tool-desc">商业性能测试工具，提供全面的性能分析和监控</div>
                </div>
              </div>
              
              <div class="tool-box">
                <div class="tool-icon"><i class="fas fa-fighter-jet"></i></div>
                <div class="tool-content">
                  <div class="tool-name">K6</div>
                  <div class="tool-desc">现代化的性能测试工具，使用JavaScript编写测试脚本</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 负载模型图表
      const loadModelCtx = document.getElementById('loadModelChart').getContext('2d');
      const loadModelChart = new Chart(loadModelCtx, {
        type: 'line',
        data: {
          labels: ['0', '5', '10', '15', '20', '25', '30'],
          datasets: [
            {
              label: '阶梯式负载',
              data: [0, 100, 100, 300, 300, 500, 500],
              borderColor: '#E67E22',
              backgroundColor: 'rgba(230, 126, 34, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.1
            },
            {
              label: '峰值负载',
              data: [0, 50, 100, 150, 500, 200, 100],
              borderColor: '#3498DB',
              backgroundColor: 'rgba(52, 152, 219, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '并发用户数',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              title: {
                display: true,
                text: '时间(分钟)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  



</body></html>