<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .section {
        margin-bottom: 15px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 250px;
      }
      .chart-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .feature-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .challenge-item {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 8px;
        margin-bottom: 8px;
        display: flex;
        align-items: flex-start;
      }
      .challenge-icon {
        background-color: #E67E22;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
        flex-shrink: 0;
      }
      .challenge-content {
        flex: 1;
      }
      .challenge-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">云原生应用测试</div>
        <div class="page-number">8 / 10</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-cloud"></i>云测试概念
            </div>
            <div class="section-content">
              <p>云测试是在云环境中执行的软件测试，利用云计算资源进行测试活动，包括测试环境的配置、测试用例的执行和测试结果的分析。</p>
              <p>云原生应用测试关注微服务架构、容器化部署和动态编排等云原生特性带来的测试挑战。</p>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-star"></i>云测试特点与优势
            </div>
            <div class="section-content">
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-expand-arrows-alt"></i></div>
                <div>弹性扩展：按需分配测试资源，支持大规模并发测试</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-dollar-sign"></i></div>
                <div>成本效益：降低基础设施投资，按使用付费</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-globe"></i></div>
                <div>全球可用：支持不同地理位置的分布式测试</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-sync-alt"></i></div>
                <div>环境一致性：容器化技术确保开发、测试和生产环境一致</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-bolt"></i></div>
                <div>快速配置：测试环境的快速创建和销毁，缩短测试周期</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-exclamation-triangle"></i>云测试挑战
            </div>
            <div class="section-content">
              <div class="challenge-item">
                <div class="challenge-icon">1</div>
                <div class="challenge-content">
                  <div class="challenge-title">分布式系统复杂性</div>
                  <div>微服务间的依赖关系和通信模式增加了测试难度</div>
                </div>
              </div>
              
              <div class="challenge-item">
                <div class="challenge-icon">2</div>
                <div class="challenge-content">
                  <div class="challenge-title">数据一致性</div>
                  <div>分布式数据存储和事务处理需要特殊的测试策略</div>
                </div>
              </div>
              
              <div class="challenge-item">
                <div class="challenge-icon">3</div>
                <div class="challenge-content">
                  <div class="challenge-title">安全与合规</div>
                  <div>多租户环境下的数据隔离和安全测试要求更高</div>
                </div>
              </div>
              
              <div class="challenge-item">
                <div class="challenge-icon">4</div>
                <div class="challenge-content">
                  <div class="challenge-title">服务弹性</div>
                  <div>需要测试系统在部分服务失效情况下的行为</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-line"></i>云测试采用趋势
            </div>
            <div class="chart-container">
              <div class="chart-title">企业云测试采用率 (2020-2025)</div>
              <canvas id="adoptionChart"></canvas>
            </div>
            <div class="section-content">
              <p>数据来源：Gartner 2024年云测试市场报告</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 云测试采用趋势图表
      const adoptionCtx = document.getElementById('adoptionChart').getContext('2d');
      const adoptionChart = new Chart(adoptionCtx, {
        type: 'line',
        data: {
          labels: ['2020', '2021', '2022', '2023', '2024', '2025(预测)'],
          datasets: [
            {
              label: '大型企业',
              data: [42, 53, 65, 76, 85, 92],
              borderColor: '#E67E22',
              backgroundColor: 'rgba(230, 126, 34, 0.2)',
              borderWidth: 2,
              pointBackgroundColor: '#E67E22'
            },
            {
              label: '中小企业',
              data: [25, 34, 45, 58, 68, 78],
              borderColor: 'rgba(255, 255, 255, 0.7)',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderWidth: 2,
              pointBackgroundColor: 'rgba(255, 255, 255, 0.7)'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '采用率 (%)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

