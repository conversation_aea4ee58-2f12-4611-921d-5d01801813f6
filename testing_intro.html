<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-around; /* Changed to space-around for better distribution */
      }
      .section {
        margin-bottom: 10px; /* Reduced margin for sections */
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .highlight {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 5px;
        height: 220px; /* Further reduced height for charts */
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .bullet-point {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .bullet-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">软件测试核心概念与价值</div>
        <div class="page-number">2 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-question-circle"></i>软件测试定义
            </div>
            <div class="section-content">
              <p>软件测试是评估软件产品或服务质量的过程，旨在：</p>
              <div class="bullet-point">
                <div class="bullet-icon"><i class="fas fa-check"></i></div>
                <div>验证软件是否满足指定的需求</div>
              </div>
              <div class="bullet-point">
                <div class="bullet-icon"><i class="fas fa-check"></i></div>
                <div>识别软件中的缺陷和问题</div>
              </div>
              <div class="bullet-point">
                <div class="bullet-icon"><i class="fas fa-check"></i></div>
                <div>评估软件的质量特性</div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-line"></i>测试ROI分析
            </div>
            <div class="section-content">
              <div class="highlight">
                <strong>早期发现缺陷的价值：</strong> 需求阶段发现的缺陷修复成本是生产环境的1/100。
              </div>
              <p>NIST研究：美国企业每年因软件缺陷损失约595亿美元，三分之一可通过更好的测试避免。</p>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-bullseye"></i>测试目标
            </div>
            <div class="section-content">
              <div class="bullet-point">
                <div class="bullet-icon"><i class="fas fa-arrow-right"></i></div>
                <div>提高软件质量和可靠性</div>
              </div>
              <div class="bullet-point">
                <div class="bullet-icon"><i class="fas fa-arrow-right"></i></div>
                <div>降低维护成本和技术债务</div>
              </div>
              <div class="bullet-point">
                <div class="bullet-icon"><i class="fas fa-arrow-right"></i></div>
                <div>增强用户体验和满意度</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-cubes"></i>ISO 25010质量模型
            </div>
            <div class="chart-container">
              <div class="chart-title">ISO 25010软件质量特性</div>
              <canvas id="qualityModelChart" width="832" height="285" style="display: block; box-sizing: border-box; height: 190px; width: 554.667px;"></canvas>
            </div>
            <div class="section-content">
              <p>ISO 25010定义了8个软件产品质量特性，为测试提供了全面的评估框架。</p>
            </div>
          </div>
          
          <div class="section">
            <div class="chart-container">
              <div class="chart-title">缺陷修复成本随阶段增长</div>
              <canvas id="costChart" width="832" height="285" style="display: block; box-sizing: border-box; height: 190px; width: 554.667px;"></canvas>
            </div>
            <div class="section-content">
              <p>数据来源：IBM Systems Sciences Institute &amp; NIST</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // ISO 25010质量模型图表
      const qualityCtx = document.getElementById('qualityModelChart').getContext('2d');
      const qualityModelChart = new Chart(qualityCtx, {
        type: 'radar',
        data: {
          labels: ['功能适合性', '性能效率', '兼容性', '可用性', '可靠性', '安全性', '可维护性', '可移植性'],
          datasets: [{
            label: 'ISO 25010质量特性',
            data: [5, 5, 5, 5, 5, 5, 5, 5],
            backgroundColor: 'rgba(230, 126, 34, 0.2)',
            borderColor: '#E67E22',
            borderWidth: 2,
            pointBackgroundColor: '#E67E22'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              beginAtZero: true,
              max: 5,
              ticks: {
                display: false
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.2)'
              },
              angleLines: {
                color: 'rgba(255, 255, 255, 0.2)'
              },
              pointLabels: {
                color: '#FFFFFF',
                font: {
                  size: 10
                }
              }
            }
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              enabled: false
            }
          },
          layout: {
            padding: {
              left: 0,
              right: 0,
              top: 0,
              bottom: 0
            }
          }
        }
      });
      
      // 缺陷修复成本图表
      const costCtx = document.getElementById('costChart').getContext('2d');
      const costChart = new Chart(costCtx, {
        type: 'bar',
        data: {
          labels: ['需求阶段', '设计阶段', '编码阶段', '测试阶段', '生产环境'],
          datasets: [{
            label: '相对修复成本',
            data: [1, 3, 10, 15, 100],
            backgroundColor: '#E67E22'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '相对成本（倍数）',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              display: false
            }
          }
        }
      });
    </script>
  



</body></html>