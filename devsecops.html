<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        height: 240px;
      }
      .chart-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .feature-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .comparison-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 6px;
      }
      .comparison-table th {
        text-align: left;
        padding: 6px;
        font-weight: bold;
        color: #E67E22;
        font-size: 14px;
      }
      .comparison-table td {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 6px;
        font-size: 14px;
      }
      .comparison-table tr td:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        font-weight: bold;
      }
      .comparison-table tr td:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">DevSecOps与测试左移右移</div>
        <div class="page-number">11 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-shield-alt"></i>DevSecOps核心理念
            </div>
            <div class="section-content">
              <p>DevSecOps将安全融入DevOps流程，实现"安全即代码"，确保全流程安全。</p>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-lock"></i></div>
                <div>安全左移：将安全测试前置到开发早期阶段</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-code-branch"></i></div>
                <div>自动化安全测试：集成到CI/CD流水线</div>
              </div>
              <div class="feature-item">
                <div class="feature-icon"><i class="fas fa-users-cog"></i></div>
                <div>共同责任：开发、测试、运维共同承担安全责任</div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-arrows-alt-h"></i>测试左移与右移
            </div>
            <div class="section-content">
              <table class="comparison-table">
                <thead>
                  <tr>
                    <th width="20%"></th>
                    <th width="40%">测试左移</th>
                    <th width="40%">测试右移</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>阶段</td>
                    <td>需求、设计、编码早期</td>
                    <td>部署、生产环境</td>
                  </tr>
                  <tr>
                    <td>目标</td>
                    <td>尽早发现缺陷，降低修复成本</td>
                    <td>监控真实用户体验，快速响应问题</td>
                  </tr>
                  <tr>
                    <td>方法</td>
                    <td>单元测试、静态分析、代码评审</td>
                    <td>A/B测试、混沌测试、用户监控</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-project-diagram"></i>DevSecOps流程
            </div>
            <div class="chart-container">
              <div class="chart-title">DevSecOps集成测试流程</div>
              <canvas id="devsecopsChart" width="555" height="210"></canvas>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-bar"></i>测试左移右移效益
            </div>
            <div class="chart-container">
              <div class="chart-title">测试左移右移带来的效益</div>
              <canvas id="benefitsChart" width="832" height="315" style="display: block; box-sizing: border-box; height: 210px; width: 554.667px;"></canvas>
            </div>
            <div class="section-content">
              <p>DORA 2024报告：测试左移的组织缺陷修复成本平均降低45%，测试右移的组织用户报告的生产问题减少37%。</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // DevSecOps流程图
      const devsecopsCtx = document.getElementById("devsecopsChart").getContext("2d");
      
      // Set canvas dimensions explicitly to match container
      devsecopsCtx.canvas.width = devsecopsCtx.canvas.parentNode.offsetWidth - 30;
      devsecopsCtx.canvas.height = devsecopsCtx.canvas.parentNode.offsetHeight - 30;

      const width = devsecopsCtx.canvas.width;
      const height = devsecopsCtx.canvas.height;
      
      devsecopsCtx.clearRect(0, 0, width, height);
      
      // Adjusted coordinates and sizes for better fit
      const stages = [
        { x: width * 0.05, y: height * 0.2, width: width * 0.15, height: height * 0.15, label: "计划", color: "rgba(230, 126, 34, 0.8)" },
        { x: width * 0.25, y: height * 0.2, width: width * 0.15, height: height * 0.15, label: "编码", color: "rgba(230, 126, 34, 0.8)" },
        { x: width * 0.45, y: height * 0.2, width: width * 0.15, height: height * 0.15, label: "构建", color: "rgba(230, 126, 34, 0.8)" },
        { x: width * 0.65, y: height * 0.2, width: width * 0.15, height: height * 0.15, label: "测试", color: "rgba(230, 126, 34, 0.8)" },
        { x: width * 0.85, y: height * 0.2, width: width * 0.15, height: height * 0.15, label: "部署", color: "rgba(230, 126, 34, 0.8)" }
      ];
      
      const secTests = [
        { x: width * 0.05, y: height * 0.5, width: width * 0.15, height: height * 0.12, label: "威胁建模", color: "rgba(255, 255, 255, 0.3)" },
        { x: width * 0.25, y: height * 0.5, width: width * 0.15, height: height * 0.12, label: "静态分析", color: "rgba(255, 255, 255, 0.3)" },
        { x: width * 0.45, y: height * 0.5, width: width * 0.15, height: height * 0.12, label: "依赖检查", color: "rgba(255, 255, 255, 0.3)" },
        { x: width * 0.65, y: height * 0.5, width: width * 0.15, height: height * 0.12, label: "动态分析", color: "rgba(255, 255, 255, 0.3)" },
        { x: width * 0.85, y: height * 0.5, width: width * 0.15, height: height * 0.12, label: "渗透测试", color: "rgba(255, 255, 255, 0.3)" }
      ];
      
      // Draw horizontal line for stages
      devsecopsCtx.beginPath();
      devsecopsCtx.moveTo(stages[0].x + stages[0].width / 2, stages[0].y + stages[0].height / 2);
      devsecopsCtx.lineTo(stages[stages.length - 1].x + stages[stages.length - 1].width / 2, stages[stages.length - 1].y + stages[stages.length - 1].height / 2);
      devsecopsCtx.strokeStyle = "rgba(255, 255, 255, 0.5)";
      devsecopsCtx.lineWidth = 2;
      devsecopsCtx.stroke();
      
      // 绘制阶段框
      function drawBox(ctx, box) {
        ctx.fillStyle = box.color;
        ctx.fillRect(box.x, box.y, box.width, box.height);
        
        ctx.strokeStyle = "rgba(255, 255, 255, 0.8)";
        ctx.lineWidth = 1;
        ctx.strokeRect(box.x, box.y, box.width, box.height);
        
        ctx.font = "12px Arial";
        ctx.fillStyle = "white";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(box.label, box.x + box.width/2, box.y + box.height/2);
      }
      
      // 绘制连接线
      function drawConnection(ctx, fromBox, toBox) {
        ctx.beginPath();
        ctx.moveTo(fromBox.x + fromBox.width/2, fromBox.y + fromBox.height);
        ctx.lineTo(toBox.x + toBox.width/2, toBox.y);
        ctx.strokeStyle = "rgba(230, 126, 34, 0.6)";
        ctx.lineWidth = 1;
        ctx.stroke();
      }
      
      // 绘制所有阶段框
      stages.forEach(stage => drawBox(devsecopsCtx, stage));
      secTests.forEach(test => drawBox(devsecopsCtx, test));
      
      // 绘制连接线
      for (let i = 0; i < stages.length; i++) {
        drawConnection(devsecopsCtx, stages[i], secTests[i]);
      }
      
      // 添加标题
      devsecopsCtx.font = "14px Arial";
      devsecopsCtx.fillStyle = "white";
      devsecopsCtx.textAlign = "center";
      devsecopsCtx.fillText("开发流程", width / 2, height * 0.1);
      devsecopsCtx.fillText("安全测试", width / 2, height * 0.4);
      
      // 添加左移右移标识
      devsecopsCtx.font = "bold 12px Arial";
      devsecopsCtx.fillStyle = "#E67E22";
      devsecopsCtx.textAlign = "center";
      devsecopsCtx.fillText("测试左移", width * 0.2, height * 0.05);
      devsecopsCtx.fillText("测试右移", width * 0.8, height * 0.05);
      
      // 绘制箭头
      devsecopsCtx.beginPath();
      devsecopsCtx.moveTo(width * 0.2, height * 0.1);
      devsecopsCtx.lineTo(width * 0.05, height * 0.1);
      devsecopsCtx.lineTo(width * 0.07, height * 0.09);
      devsecopsCtx.moveTo(width * 0.05, height * 0.1);
      devsecopsCtx.lineTo(width * 0.07, height * 0.11);
      devsecopsCtx.strokeStyle = "#E67E22";
      devsecopsCtx.lineWidth = 2;
      devsecopsCtx.stroke();
      
      devsecopsCtx.beginPath();
      devsecopsCtx.moveTo(width * 0.8, height * 0.1);
      devsecopsCtx.lineTo(width * 0.95, height * 0.1);
      devsecopsCtx.lineTo(width * 0.93, height * 0.09);
      devsecopsCtx.moveTo(width * 0.95, height * 0.1);
      devsecopsCtx.lineTo(width * 0.93, height * 0.11);
      devsecopsCtx.strokeStyle = "#E67E22";
      devsecopsCtx.lineWidth = 2;
      devsecopsCtx.stroke();
      
      // 测试左移右移效益图表
      const benefitsCtx = document.getElementById("benefitsChart").getContext("2d");
      const benefitsChart = new Chart(benefitsCtx, {
        type: "bar",
        data: {
          labels: ["缺陷修复成本降低", "生产问题减少", "上市时间缩短", "用户满意度提升"],
          datasets: [
            {
              label: "测试左移",
              data: [45, 30, 25, 20],
              backgroundColor: "rgba(230, 126, 34, 0.8)",
              borderColor: "rgba(230, 126, 34, 1)",
              borderWidth: 1
            },
            {
              label: "测试右移",
              data: [15, 37, 18, 32],
              backgroundColor: "rgba(255, 255, 255, 0.5)",
              borderColor: "rgba(255, 255, 255, 0.8)",
              borderWidth: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: "改进百分比 (%)",
                color: "#FFFFFF"
              },
              grid: {
                color: "rgba(255, 255, 255, 0.1)"
              },
              ticks: {
                color: "#FFFFFF"
              }
            },
            x: {
              grid: {
                color: "rgba(255, 255, 255, 0.1)"
              },
              ticks: {
                color: "#FFFFFF"
              }
            }
          },
          plugins: {
            legend: {
              labels: {
                color: "#FFFFFF"
              }
            }
          }
        }
      });
    </script>
  



</body></html>