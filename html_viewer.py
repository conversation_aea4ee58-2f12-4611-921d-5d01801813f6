import csv
import sys
import os
import webview
import urllib.parse

class HTMLViewer:
    def __init__(self, csv_file):
        self.index = 0
        self.pages = []
        # 读取 outline.csv
        if not os.path.exists(csv_file):
            raise FileNotFoundError(f"未找到文件: {csv_file}")
        with open(csv_file, newline='', encoding='utf-8') as f:
            reader = csv.reader(f)
            # 按页码排序
            self.pages = [row[1] for row in sorted(reader, key=lambda x: int(x[0]))]
        if not self.pages:
            raise ValueError("CSV 文件为空或格式错误")

    def get_url(self, index):
        filepath = os.path.abspath(self.pages[index])
        # 使用 file:/// 前缀（Windows 下需要三个斜杠）
        file_url = "file:///" + filepath.replace("\\", "/")
        if os.path.exists(filepath):
            return file_url
        else:
            # 如果文件不存在，返回一个内嵌的错误页面，避免窗口空白
            msg = f"<html><body><h2>文件未找到</h2><p>{filepath}</p></body></html>"
            return "data:text/html," + urllib.parse.quote(msg)

    def show_page(self, index, window):
        if 0 <= index < len(self.pages):
            self.index = index
            url = self.get_url(self.index)
            window.load_url(url)
            window.set_title(f"HTML Viewer - Page {self.index+1}/{len(self.pages)}")

    def prev_page(self, window):
        if self.index > 0:
            self.show_page(self.index - 1, window)
        else:
            # 循环到最后一页
            self.show_page(len(self.pages) - 1, window)

    def next_page(self, window):
        if self.index < len(self.pages) - 1:
            self.show_page(self.index + 1, window)
        else:
            # 循环到第一页
            self.show_page(0, window)

    def _bind_keys(self, window):
        js = """
        window.addEventListener('DOMContentLoaded', function() {
            document.addEventListener('keydown', function(event) {
                if (event.key === 'ArrowUp') {
                    if (window.pywebview && window.pywebview.api) {
                        window.pywebview.api.prev_page();
                    }
                } else if (event.key === 'ArrowDown') {
                    if (window.pywebview && window.pywebview.api) {
                        window.pywebview.api.next_page();
                    }
                }
            });
        });
        """
        window.evaluate_js(js)
   
class Api:
    def __init__(self, viewer):
        self.viewer = viewer
        self.window = None

    def set_window(self, window):
        self.window = window

    def prev_page(self):
        if self.window:
            self.viewer.prev_page(self.window)

    def next_page(self):
        if self.window:
            self.viewer.next_page(self.window)

if __name__ == "__main__":
    csv_file = "outline.csv"
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]

    viewer = HTMLViewer(csv_file)
    api = Api(viewer)
    window = webview.create_window(
        "HTML Viewer",
        viewer.get_url(viewer.index),
        width=1280,
        height=720,
        min_size=(1280, 720),
        js_api=api
    )
    api.set_window(window)
    # 绑定 loaded 事件，兼容不同签名（有时会传入 sender 参数）
    window.events.loaded += lambda sender=None: viewer._bind_keys(window)

    # 使用 mshtml 后端（IE 内核），在无法使用 Edge WebView2 时更稳定地显示本地 file:// 页面
    webview.start(gui="mshtml", http_server=False, debug=False)
