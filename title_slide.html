<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
      }
      .title {
        font-size: 60px;
        font-weight: bold;
        margin-top: 80px;
        text-align: center;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        z-index: 10;
      }
      .subtitle {
        font-size: 36px;
        margin-top: 30px;
        text-align: center;
        color: #E67E22;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        z-index: 10;
      }
      .presenter {
        font-size: 24px;
        margin-top: 100px;
        text-align: center;
        color: #FFFFFF;
        z-index: 10;
      }
      .date {
        font-size: 20px;
        margin-top: 20px;
        text-align: center;
        color: #FFFFFF;
        z-index: 10;
      }
      .background-circuit {
        position: absolute;
        bottom: -50px;
        right: -50px;
        width: 600px;
        height: 600px;
        opacity: 0.1;
        z-index: 1;
      }
      .tech-icons {
        position: absolute;
        bottom: 40px;
        left: 40px;
        display: flex;
        gap: 20px;
        z-index: 10;
      }
      .tech-icon {
        font-size: 24px;
        color: #E67E22;
        background-color: rgba(255,255,255,0.1);
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .version-tag {
        position: absolute;
        top: 20px;
        right: 20px;
        background-color: #E67E22;
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: bold;
      }
      .highlight-box {
        position: absolute;
        bottom: 40px;
        right: 40px;
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 15px;
        max-width: 400px;
        z-index: 10;
      }
      .highlight-title {
        font-size: 18px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 5px;
      }
      .highlight-content {
        font-size: 14px;
        color: #FFFFFF;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="version-tag">2025版</div>
      
      <div class="title">研发团队测试培训</div>
      <div class="subtitle">前沿理论与实践：赋能研发，构建高质量软件</div>
      
      <div class="presenter">讲师：赵阳</div>
      <div class="date">2025年8月18日</div>
      
      <div class="tech-icons">
        <div class="tech-icon"><i class="fas fa-vial"></i></div>
        <div class="tech-icon"><i class="fas fa-code"></i></div>
        <div class="tech-icon"><i class="fas fa-robot"></i></div>
        <div class="tech-icon"><i class="fas fa-cloud"></i></div>
        <div class="tech-icon"><i class="fas fa-shield-alt"></i></div>
      </div>
      
      <div class="highlight-box">
        <div class="highlight-title">培训亮点</div>
        <div class="highlight-content">
          • AI驱动的智能测试最新实践<br>
          • DevSecOps与测试左移右移<br>
          • 云原生应用测试策略<br>
          • 测试自动化框架精选(Cypress/Playwright)<br>
          • 混沌工程与弹性测试
        </div>
      </div>
      
      <svg class="background-circuit" viewBox="0 0 100 100">
        <path d="M10,30 L40,30 L40,10 L60,10 L60,30 L90,30" stroke="#E67E22" stroke-width="0.5" fill="none"></path>
        <path d="M10,50 L30,50 L30,70 L50,70 L50,40 L70,40 L70,60 L90,60" stroke="#E67E22" stroke-width="0.5" fill="none"></path>
        <path d="M10,80 L50,80 L50,90 L90,90" stroke="#E67E22" stroke-width="0.5" fill="none"></path>
        <circle cx="40" cy="30" r="2" fill="#E67E22"></circle>
        <circle cx="60" cy="10" r="2" fill="#E67E22"></circle>
        <circle cx="30" cy="50" r="2" fill="#E67E22"></circle>
        <circle cx="50" cy="70" r="2" fill="#E67E22"></circle>
        <circle cx="70" cy="40" r="2" fill="#E67E22"></circle>
        <circle cx="50" cy="80" r="2" fill="#E67E22"></circle>
      </svg>
    </div>
  



</body></html>