<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 8px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .strategy-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
      }
      .strategy-title {
        font-size: 16px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
      }
      .strategy-title i {
        margin-right: 8px;
      }
      .strategy-content {
        font-size: 13px;
        opacity: 0.9;
      }
      .method-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 6px;
      }
      .method-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .method-content {
        flex: 1;
      }
      .method-title {
        font-weight: bold;
        margin-bottom: 2px;
      }
      .method-desc {
        font-size: 13px;
        opacity: 0.9;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        height: 260px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .tool-box {
        background-color: rgba(52, 152, 219, 0.2);
        border-left: 4px solid #3498DB;
        padding: 6px;
        margin: 4px 0;
        border-radius: 0 6px 6px 0;
        font-size: 13px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">硬件测试策略与方法</div>
        <div class="page-number">24 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-microchip"></i>专用硬件测试策略
            </div>
            <div class="section-content">
              <div class="strategy-box">
                <div class="strategy-title">
                  <i class="fas fa-search"></i>功能验证测试
                </div>
                <div class="strategy-content">
                  验证ASIC/FPGA的逻辑功能正确性，包括数字信号处理、控制逻辑、接口协议等核心功能的验证。
                </div>
              </div>
              
              <div class="strategy-box">
                <div class="strategy-title">
                  <i class="fas fa-tachometer-alt"></i>性能基准测试
                </div>
                <div class="strategy-content">
                  测试处理速度、吞吐量、延迟等关键性能指标，确保满足设计规格要求。
                </div>
              </div>
              
              <div class="strategy-box">
                <div class="strategy-title">
                  <i class="fas fa-thermometer-half"></i>环境适应性测试
                </div>
                <div class="strategy-content">
                  在不同温度、湿度、电压条件下测试硬件稳定性和可靠性。
                </div>
              </div>
              
              <div class="strategy-box">
                <div class="strategy-title">
                  <i class="fas fa-bolt"></i>电气特性测试
                </div>
                <div class="strategy-content">
                  测试功耗、信号完整性、电磁兼容性等电气特性指标。
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-server"></i>通用硬件测试方法
            </div>
            <div class="section-content">
              <div class="method-box">
                <div class="method-icon"><i class="fas fa-check-circle"></i></div>
                <div class="method-content">
                  <div class="method-title">兼容性验证</div>
                  <div class="method-desc">验证COTS硬件与系统其他组件的兼容性，包括接口、协议、驱动程序等。</div>
                </div>
              </div>
              
              <div class="method-box">
                <div class="method-icon"><i class="fas fa-chart-line"></i></div>
                <div class="method-content">
                  <div class="method-title">负载压力测试</div>
                  <div class="method-desc">在高负载条件下测试硬件性能和稳定性，验证是否满足系统需求。</div>
                </div>
              </div>
              
              <div class="method-box">
                <div class="method-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="method-content">
                  <div class="method-title">故障恢复测试</div>
                  <div class="method-desc">测试硬件在故障情况下的恢复能力和冗余机制的有效性。</div>
                </div>
              </div>
              
              <div class="method-box">
                <div class="method-icon"><i class="fas fa-cog"></i></div>
                <div class="method-content">
                  <div class="method-title">配置管理测试</div>
                  <div class="method-desc">验证硬件配置的正确性和配置变更的影响。</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tools"></i>硬件测试工具与设备
            </div>
            <div class="section-content">
              <div class="tool-box">
                <strong>逻辑分析仪：</strong> 用于数字信号分析和时序验证，支持多通道并行分析
              </div>
              <div class="tool-box">
                <strong>示波器：</strong> 模拟和数字信号波形分析，信号完整性测试
              </div>
              <div class="tool-box">
                <strong>网络分析仪：</strong> 射频和微波器件的频域特性测试
              </div>
              <div class="tool-box">
                <strong>ICT测试仪：</strong> 在线测试仪，用于电路板级功能和故障检测
              </div>
              <div class="tool-box">
                <strong>边界扫描测试：</strong> JTAG接口测试，用于复杂电路板的测试
              </div>
              <div class="tool-box">
                <strong>环境测试箱：</strong> 温度、湿度、振动等环境条件模拟
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-bar"></i>硬件测试覆盖率分析
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">硬件测试类型覆盖率</div>
                <canvas id="hardwareTestChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 硬件测试覆盖率分析图表
      const hardwareTestCtx = document.getElementById('hardwareTestChart').getContext('2d');
      const hardwareTestChart = new Chart(hardwareTestCtx, {
        type: 'bar',
        data: {
          labels: ['功能测试', '性能测试', '环境测试', '兼容性测试', '可靠性测试'],
          datasets: [{
            label: '专用硬件',
            data: [95, 90, 85, 70, 88],
            backgroundColor: 'rgba(230, 126, 34, 0.7)',
            borderColor: '#E67E22',
            borderWidth: 1
          }, {
            label: '通用硬件',
            data: [85, 80, 75, 95, 82],
            backgroundColor: 'rgba(52, 152, 219, 0.7)',
            borderColor: '#3498DB',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '覆盖率(%)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

