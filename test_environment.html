<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .practice-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .practice-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .practice-content {
        flex: 1;
      }
      .practice-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .practice-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 220px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试环境搭建与管理：降本增效实践</div>
        <div class="page-number">15 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-robot"></i>自动化与基础设施即代码 (IaC)
            </div>
            <div class="section-content">
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-cogs"></i></div>
                <div class="practice-content">
                  <div class="practice-title">环境自动化部署</div>
                  <div class="practice-desc">通过Ansible、Puppet等工具实现环境一键部署，减少手动操作和错误。</div>
                </div>
              </div>
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-code"></i></div>
                <div class="practice-content">
                  <div class="practice-title">基础设施即代码 (IaC)</div>
                  <div class="practice-desc">使用Terraform、CloudFormation等将环境配置代码化，实现版本控制和按需创建销毁。</div>
                </div>
              </div>
              <div class="highlight-box">
                <strong>效益：</strong> 环境准备时间缩短80%，环境一致性大幅提升，成本降低40%。
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-boxes"></i>容器化与虚拟化技术
            </div>
            <div class="section-content">
              <div class="practice-box">
                <div class="practice-icon"><i class="fab fa-docker"></i></div>
                <div class="practice-content">
                  <div class="practice-title">Docker容器化</div>
                  <div class="practice-desc">轻量级、可移植的运行环境，实现快速启动、环境隔离和一致性。</div>
                </div>
              </div>
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-server"></i></div>
                <div class="practice-content">
                  <div class="practice-title">虚拟机 (VM) 技术</div>
                  <div class="practice-desc">提供完整操作系统隔离，适用于特定场景，支持快照和恢复。</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-cloud"></i>按需供应与弹性伸缩
            </div>
            <div class="section-content">
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-dollar-sign"></i></div>
                <div class="practice-content">
                  <div class="practice-title">按需创建与销毁</div>
                  <div class="practice-desc">利用云服务和IaC，只在需要时启动环境，测试后立即销毁，按量付费。</div>
                </div>
              </div>
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-moon"></i></div>
                <div class="practice-content">
                  <div class="practice-title">环境睡眠与唤醒</div>
                  <div class="practice-desc">非工作时间自动睡眠，停止计费，有效减少云资源费用。</div>
                </div>
              </div>
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-share-alt"></i></div>
                <div class="practice-content">
                  <div class="practice-title">环境池化与复用</div>
                  <div class="practice-desc">建立标准化环境池，通过预订系统共享和复用资源，提高利用率。</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-database"></i>测试数据管理
            </div>
            <div class="section-content">
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-user-secret"></i></div>
                <div class="practice-content">
                  <div class="practice-title">敏感数据脱敏</div>
                  <div class="practice-desc">确保数据隐私合规，同时保持数据业务逻辑完整性。</div>
                </div>
              </div>
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-random"></i></div>
                <div class="practice-content">
                  <div class="practice-title">测试数据生成与管理</div>
                  <div class="practice-desc">自动化生成大量多样化数据，缩短数据准备周期。</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
  



</body></html>