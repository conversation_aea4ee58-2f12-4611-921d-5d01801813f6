<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .section {
        margin-bottom: 15px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 250px;
      }
      .chart-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .phase {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 8px;
        margin-bottom: 8px;
        display: flex;
        align-items: flex-start;
      }
      .phase-number {
        background-color: #E67E22;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
        flex-shrink: 0;
      }
      .phase-content {
        flex: 1;
      }
      .phase-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .key-metric {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }
      .metric-icon {
        color: #E67E22;
        margin-right: 8px;
      }
      .metric-value {
        font-weight: bold;
        margin-right: 5px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试流程与生命周期</div>
        <div class="page-number">5 / 10</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-sync-alt"></i>测试生命周期五阶段
            </div>
            <div class="section-content">
              <div class="phase">
                <div class="phase-number">1</div>
                <div class="phase-content">
                  <div class="phase-title">测试规划</div>
                  <div>确定测试范围、目标、策略、资源和进度计划</div>
                </div>
              </div>
              
              <div class="phase">
                <div class="phase-number">2</div>
                <div class="phase-content">
                  <div class="phase-title">测试分析与设计</div>
                  <div>分析测试基础、识别测试条件、设计测试用例</div>
                </div>
              </div>
              
              <div class="phase">
                <div class="phase-number">3</div>
                <div class="phase-content">
                  <div class="phase-title">测试实现与执行</div>
                  <div>准备测试环境、执行测试用例、记录测试结果</div>
                </div>
              </div>
              
              <div class="phase">
                <div class="phase-number">4</div>
                <div class="phase-content">
                  <div class="phase-title">评估与报告</div>
                  <div>分析测试结果、评估退出标准、生成测试报告</div>
                </div>
              </div>
              
              <div class="phase">
                <div class="phase-number">5</div>
                <div class="phase-content">
                  <div class="phase-title">测试闭环活动</div>
                  <div>总结经验教训、归档测试资产、改进测试过程</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-pie"></i>测试时间分配
            </div>
            <div class="chart-container">
              <div class="chart-title">测试生命周期各阶段时间分配</div>
              <canvas id="timeAllocationChart"></canvas>
            </div>
            <div class="section-content">
              <p>数据来源：ISTQB测试实践调查报告 (2024)</p>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tachometer-alt"></i>测试效率关键指标
            </div>
            <div class="section-content">
              <div class="key-metric">
                <div class="metric-icon"><i class="fas fa-crosshairs"></i></div>
                <div class="metric-value">75-85%</div>
                <div>测试用例执行率（行业标准）</div>
              </div>
              <div class="key-metric">
                <div class="metric-icon"><i class="fas fa-bug"></i></div>
                <div class="metric-value">< 10%</div>
                <div>缺陷泄漏率（高成熟度团队）</div>
              </div>
              <div class="key-metric">
                <div class="metric-icon"><i class="fas fa-clock"></i></div>
                <div class="metric-value">< 48小时</div>
                <div>缺陷平均修复时间（敏捷团队）</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 测试时间分配图表
      const timeCtx = document.getElementById('timeAllocationChart').getContext('2d');
      const timeAllocationChart = new Chart(timeCtx, {
        type: 'pie',
        data: {
          labels: ['测试规划 (15%)', '分析与设计 (30%)', '实现与执行 (40%)', '评估与报告 (10%)', '闭环活动 (5%)'],
          datasets: [{
            data: [15, 30, 40, 10, 5],
            backgroundColor: [
              'rgba(230, 126, 34, 0.9)',
              'rgba(230, 126, 34, 0.7)',
              'rgba(230, 126, 34, 0.5)',
              'rgba(230, 126, 34, 0.3)',
              'rgba(230, 126, 34, 0.2)'
            ],
            borderColor: [
              'rgba(255, 255, 255, 0.8)',
              'rgba(255, 255, 255, 0.8)',
              'rgba(255, 255, 255, 0.8)',
              'rgba(255, 255, 255, 0.8)',
              'rgba(255, 255, 255, 0.8)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right',
              labels: {
                color: '#FFFFFF',
                font: {
                  size: 12
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

