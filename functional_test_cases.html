<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .design-method {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .method-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .method-content {
        flex: 1;
      }
      .method-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .method-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .example-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 6px;
      }
      .example-table th {
        text-align: left;
        padding: 6px;
        font-weight: bold;
        color: #E67E22;
      }
      .example-table td {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 6px;
        font-size: 14px;
      }
      .example-table tr td:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        font-weight: bold;
      }
      .example-table tr td:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .test-case-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
      }
      .test-case-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        color: #E67E22;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试用例设计实践：功能测试</div>
        <div class="page-number">8 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-clipboard-check"></i>功能测试用例设计原则
            </div>
            <div class="section-content">
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-check-double"></i></div>
                <div class="method-content">
                  <div class="method-title">需求覆盖</div>
                  <div class="method-desc">确保每个功能需求点都有对应的测试用例</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-random"></i></div>
                <div class="method-content">
                  <div class="method-title">多样性</div>
                  <div class="method-desc">结合多种测试设计方法，如等价类划分、边界值分析等</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-balance-scale"></i></div>
                <div class="method-content">
                  <div class="method-title">正反平衡</div>
                  <div class="method-desc">同时设计正向（有效）和反向（无效）测试用例</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-sign-in-alt"></i>登录功能测试用例
            </div>
            <div class="section-content">
              <div class="test-case-box">
                <div class="test-case-title">TC-001: 有效用户名和密码登录</div>
                <table class="example-table">
                  <tbody><tr>
                    <td width="30%">测试数据</td>
                    <td>用户名: <EMAIL><br>密码: Valid123!</td>
                  </tr>
                  <tr>
                    <td>步骤</td>
                    <td>1. 输入有效用户名<br>2. 输入有效密码<br>3. 点击登录按钮</td>
                  </tr>
                  <tr>
                    <td>预期结果</td>
                    <td>1. 登录成功<br>2. 跳转到首页<br>3. 显示欢迎信息</td>
                  </tr>
                </tbody></table>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-file-alt"></i>表单提交功能测试用例
            </div>
            <div class="section-content">
              <div class="test-case-box">
                <div class="test-case-title">TC-101: 所有必填字段有效提交</div>
                <table class="example-table">
                  <tbody><tr>
                    <td width="30%">测试数据</td>
                    <td>姓名: "张三"<br>电话: "13812345678"<br>邮箱: "<EMAIL>"</td>
                  </tr>
                  <tr>
                    <td>步骤</td>
                    <td>1. 填写所有必填字段<br>2. 点击提交按钮</td>
                  </tr>
                  <tr>
                    <td>预期结果</td>
                    <td>1. 表单提交成功<br>2. 显示成功提示信息<br>3. 数据正确保存到数据库</td>
                  </tr>
                </tbody></table>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-code-branch"></i>功能测试用例设计技巧
            </div>
            <div class="section-content">
              <div class="highlight-box">
                <strong>组合测试设计方法：</strong>
                <p>结合等价类划分和边界值分析可以更全面地覆盖测试场景。例如，登录功能测试中：</p>
                <ul>
                  <li>等价类划分：有效用户名/无效用户名，有效密码/无效密码</li>
                  <li>边界值分析：密码长度边界（最小长度、最小长度-1等）</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
  



</body></html>