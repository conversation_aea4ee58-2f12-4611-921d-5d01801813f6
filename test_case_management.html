<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .design-method {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .method-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .method-content {
        flex: 1;
      }
      .method-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .method-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 220px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .tool-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
      }
      .tool-icon {
        font-size: 24px;
        color: #E67E22;
        margin-right: 15px;
      }
      .tool-content {
        flex: 1;
      }
      .tool-name {
        font-weight: bold;
        margin-bottom: 2px;
      }
      .tool-desc {
        font-size: 14px;
        opacity: 0.9;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试用例管理与维护</div>
        <div class="page-number">10 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-folder-tree"></i>测试用例组织与命名规范
            </div>
            <div class="section-content">
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-sitemap"></i></div>
                <div class="method-content">
                  <div class="method-title">层次化组织</div>
                  <div class="method-desc">按模块 &gt; 功能 &gt; 场景进行分层组织</div>
                </div>
              </div>
              
              <div class="design-method">
                <div class="method-icon"><i class="fas fa-tag"></i></div>
                <div class="method-content">
                  <div class="method-title">命名规范</div>
                  <div class="method-desc">TC-[模块]-[功能]-[编号]，如TC-Login-Auth-001</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-sync-alt"></i>测试用例评审与更新
            </div>
            <div class="section-content">
              <div class="highlight-box">
                <strong>测试用例评审要点：</strong>
                <ul>
                  <li>需求覆盖完整性</li>
                  <li>测试步骤清晰度</li>
                  <li>预期结果明确性</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-pie"></i>测试覆盖率分析
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">测试覆盖率类型</div>
                <canvas id="coverageChart" width="832" height="285" style="display: block; box-sizing: border-box; height: 190px; width: 554.667px;"></canvas>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tools"></i>测试用例管理工具
            </div>
            <div class="section-content">
              <div class="tool-box">
                <div class="tool-icon"><i class="fas fa-clipboard-list"></i></div>
                <div class="tool-content">
                  <div class="tool-name">TestRail</div>
                  <div class="tool-desc">全面的测试用例管理平台</div>
                </div>
              </div>
              
              <div class="tool-box">
                <div class="tool-icon"><i class="fas fa-bug"></i></div>
                <div class="tool-content">
                  <div class="tool-name">Jira + Xray</div>
                  <div class="tool-desc">Jira插件，提供测试用例管理功能</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 测试覆盖率类型图表
      const coverageCtx = document.getElementById('coverageChart').getContext('2d');
      const coverageChart = new Chart(coverageCtx, {
        type: 'pie',
        data: {
          labels: ['需求覆盖率', '代码覆盖率', '功能覆盖率', '风险覆盖率'],
          datasets: [{
            data: [35, 30, 20, 15],
            backgroundColor: [
              '#E67E22',
              '#3498DB',
              '#2ECC71',
              '#9B59B6'
            ],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right',
              labels: {
                color: '#FFFFFF',
                font: {
                  size: 12
                }
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return context.label + ': ' + context.raw + '%';
                }
              }
            }
          }
        }
      });
    </script>
  



</body></html>