<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .career-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
      }
      .career-title {
        font-size: 18px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
      }
      .career-title i {
        margin-right: 8px;
      }
      .career-desc {
        font-size: 14px;
        opacity: 0.9;
        margin-bottom: 5px;
      }
      .skill-tag {
        display: inline-block;
        background-color: rgba(230, 126, 34, 0.2);
        color: #FFFFFF;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        margin-right: 5px;
        margin-bottom: 5px;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 220px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试人员成长路径</div>
        <div class="page-number">24 / 28</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-road"></i>测试职业发展路线
            </div>
            <div class="section-content">
              <div class="career-box">
                <div class="career-title">
                  <i class="fas fa-user"></i>初级测试工程师
                </div>
                <div class="career-desc">
                  负责执行测试用例，记录缺陷，参与测试计划制定
                </div>
                <div>
                  <span class="skill-tag">手工测试</span>
                  <span class="skill-tag">基础脚本</span>
                  <span class="skill-tag">缺陷报告</span>
                </div>
              </div>
              
              <div class="career-box">
                <div class="career-title">
                  <i class="fas fa-user-tie"></i>高级测试工程师
                </div>
                <div class="career-desc">
                  设计测试策略，编写测试计划，指导初级测试人员
                </div>
                <div>
                  <span class="skill-tag">测试设计</span>
                  <span class="skill-tag">自动化测试</span>
                  <span class="skill-tag">性能测试</span>
                </div>
              </div>
              
              <div class="career-box">
                <div class="career-title">
                  <i class="fas fa-laptop-code"></i>测试开发工程师
                </div>
                <div class="career-desc">
                  开发测试框架和工具，实现高度自动化的测试解决方案
                </div>
                <div>
                  <span class="skill-tag">编程能力</span>
                  <span class="skill-tag">框架设计</span>
                  <span class="skill-tag">CI/CD</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-line"></i>测试职业发展趋势
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">测试角色需求变化趋势</div>
                <canvas id="careerTrendChart" width="832" height="285" style="display: block; box-sizing: border-box; height: 190px; width: 554.667px;"></canvas>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-crown"></i>高阶测试职业路径
            </div>
            <div class="section-content">
              <div class="career-box">
                <div class="career-title">
                  <i class="fas fa-sitemap"></i>测试架构师
                </div>
                <div class="career-desc">
                  设计测试架构，制定测试标准，推动测试技术创新
                </div>
                <div>
                  <span class="skill-tag">架构设计</span>
                  <span class="skill-tag">技术选型</span>
                  <span class="skill-tag">质量战略</span>
                </div>
              </div>
              
              <div class="career-box">
                <div class="career-title">
                  <i class="fas fa-users-cog"></i>测试管理岗
                </div>
                <div class="career-desc">
                  管理测试团队，协调资源，对接业务需求，推动质量文化
                </div>
                <div>
                  <span class="skill-tag">团队管理</span>
                  <span class="skill-tag">资源规划</span>
                  <span class="skill-tag">流程优化</span>
                </div>
              </div>
              
              <div class="highlight-box">
                <strong>认证与进阶：</strong>
                <p>ISTQB认证、敏捷测试认证、安全测试认证等专业资质可以加速职业发展</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 测试角色需求变化趋势图表
      const careerTrendCtx = document.getElementById('careerTrendChart').getContext('2d');
      const careerTrendChart = new Chart(careerTrendCtx, {
        type: 'line',
        data: {
          labels: ['2020', '2021', '2022', '2023', '2024', '2025'],
          datasets: [
            {
              label: '手工测试',
              data: [100, 90, 80, 70, 60, 50],
              borderColor: '#E67E22',
              backgroundColor: 'rgba(230, 126, 34, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.1
            },
            {
              label: '自动化测试',
              data: [60, 70, 80, 90, 100, 110],
              borderColor: '#3498DB',
              backgroundColor: 'rgba(52, 152, 219, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.1
            },
            {
              label: 'AI测试',
              data: [10, 20, 30, 50, 70, 90],
              borderColor: '#2ECC71',
              backgroundColor: 'rgba(46, 204, 113, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '需求指数(基准=100)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          }
        }
      });
    </script>
  



</body></html>