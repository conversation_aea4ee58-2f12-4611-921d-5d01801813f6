<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 8px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .phase-box {
        background-color: rgba(46, 204, 113, 0.2);
        border-left: 4px solid #2ECC71;
        padding: 8px;
        margin-bottom: 6px;
        border-radius: 0 6px 6px 0;
      }
      .phase-title {
        font-size: 16px;
        font-weight: bold;
        color: #2ECC71;
        margin-bottom: 3px;
        display: flex;
        align-items: center;
      }
      .phase-title i {
        margin-right: 8px;
      }
      .phase-content {
        font-size: 13px;
        opacity: 0.9;
      }
      .method-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 5px;
      }
      .method-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .method-content {
        flex: 1;
      }
      .method-title {
        font-weight: bold;
        margin-bottom: 2px;
      }
      .method-desc {
        font-size: 13px;
        opacity: 0.9;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        height: 300px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">复杂系统测试执行流程</div>
        <div class="page-number">19 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-route"></i>分阶段执行策略
            </div>
            <div class="section-content">
              <div class="phase-box">
                <div class="phase-title">
                  <i class="fas fa-play"></i>阶段1：基础组件验证
                </div>
                <div class="phase-content">
                  独立验证各个硬件和软件组件的基本功能，确保每个组件在隔离环境下工作正常。
                </div>
              </div>
              
              <div class="phase-box">
                <div class="phase-title">
                  <i class="fas fa-link"></i>阶段2：接口集成测试
                </div>
                <div class="phase-content">
                  验证相邻组件间的接口连接，包括数据传输、协议兼容性、时序同步等关键接口功能。
                </div>
              </div>
              
              <div class="phase-box">
                <div class="phase-title">
                  <i class="fas fa-layer-group"></i>阶段3：子系统集成
                </div>
                <div class="phase-content">
                  将相关组件组合成功能子系统，验证子系统内部的协调配合和整体功能完整性。
                </div>
              </div>
              
              <div class="phase-box">
                <div class="phase-title">
                  <i class="fas fa-globe"></i>阶段4：系统级验证
                </div>
                <div class="phase-content">
                  完整系统的端到端测试，验证业务流程、性能指标、安全机制等系统级需求。
                </div>
              </div>
              
              <div class="phase-box">
                <div class="phase-title">
                  <i class="fas fa-check-circle"></i>阶段5：验收确认
                </div>
                <div class="phase-content">
                  在真实或接近真实的环境中进行最终验收测试，确认系统满足所有业务和技术要求。
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-cogs"></i>执行关键技术
            </div>
            <div class="section-content">
              <div class="method-box">
                <div class="method-icon"><i class="fas fa-robot"></i></div>
                <div class="method-content">
                  <div class="method-title">自动化测试编排</div>
                  <div class="method-desc">使用测试编排工具自动化执行测试序列，确保测试的一致性和可重复性。</div>
                </div>
              </div>
              
              <div class="method-box">
                <div class="method-icon"><i class="fas fa-database"></i></div>
                <div class="method-content">
                  <div class="method-title">测试数据管理</div>
                  <div class="method-desc">建立统一的测试数据管理机制，确保测试数据的一致性、完整性和可追溯性。</div>
                </div>
              </div>
              
              <div class="method-box">
                <div class="method-icon"><i class="fas fa-eye"></i></div>
                <div class="method-content">
                  <div class="method-title">实时监控分析</div>
                  <div class="method-desc">部署全面的监控系统，实时收集系统运行数据，快速识别异常和性能瓶颈。</div>
                </div>
              </div>
              
              <div class="method-box">
                <div class="method-icon"><i class="fas fa-sync-alt"></i></div>
                <div class="method-content">
                  <div class="method-title">并行测试执行</div>
                  <div class="method-desc">在不同层次和模块间并行执行测试，最大化测试效率，缩短整体测试周期。</div>
                </div>
              </div>
              
              <div class="method-box">
                <div class="method-icon"><i class="fas fa-bug"></i></div>
                <div class="method-content">
                  <div class="method-title">故障快速定位</div>
                  <div class="method-desc">建立故障诊断机制，通过日志分析、性能监控等手段快速定位问题根因。</div>
                </div>
              </div>
              
              <div class="method-box">
                <div class="method-icon"><i class="fas fa-undo"></i></div>
                <div class="method-content">
                  <div class="method-title">环境快速恢复</div>
                  <div class="method-desc">建立测试环境的快速备份和恢复机制，确保测试环境的稳定性和可用性。</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-chart-bar"></i>执行效率分析
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">测试执行阶段时间分布</div>
                <canvas id="executionChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 测试执行效率分析图表
      const executionCtx = document.getElementById('executionChart').getContext('2d');
      const executionChart = new Chart(executionCtx, {
        type: 'bar',
        data: {
          labels: ['基础验证', '接口集成', '子系统集成', '系统验证', '验收确认'],
          datasets: [{
            label: '计划时间(天)',
            data: [15, 20, 25, 30, 10],
            backgroundColor: 'rgba(230, 126, 34, 0.7)',
            borderColor: '#E67E22',
            borderWidth: 1
          }, {
            label: '实际时间(天)',
            data: [18, 25, 30, 35, 12],
            backgroundColor: 'rgba(52, 152, 219, 0.7)',
            borderColor: '#3498DB',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '时间(天)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

