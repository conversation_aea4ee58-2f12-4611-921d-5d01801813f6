<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .section {
        margin-bottom: 15px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .principle {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 8px;
        margin-bottom: 8px;
        display: flex;
        align-items: flex-start;
      }
      .principle-number {
        background-color: #E67E22;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
        flex-shrink: 0;
      }
      .principle-content {
        flex: 1;
      }
      .principle-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 280px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .example {
        font-size: 13px;
        font-style: italic;
        color: rgba(255, 255, 255, 0.8);
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试理论基石：七大原则与测试金字塔</div>
        <div class="page-number">3 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-book"></i>ISTQB七大测试原则
            </div>
            <div class="section-content">
              <div class="principle">
                <div class="principle-number">1</div>
                <div class="principle-content">
                  <div class="principle-title">测试显示缺陷的存在</div>
                  <div>测试可以证明缺陷存在，但不能证明没有缺陷。</div>
                </div>
              </div>
              
              <div class="principle">
                <div class="principle-number">2</div>
                <div class="principle-content">
                  <div class="principle-title">穷尽测试是不可能的</div>
                  <div>测试所有的输入组合和前提条件是不现实的，需要基于风险进行测试。</div>
                </div>
              </div>
              
              <div class="principle">
                <div class="principle-number">3</div>
                <div class="principle-content">
                  <div class="principle-title">尽早测试</div>
                  <div>测试活动应尽早开始，并贯穿整个软件开发生命周期。</div>
                </div>
              </div>
              
              <div class="principle">
                <div class="principle-number">4</div>
                <div class="principle-content">
                  <div class="principle-title">缺陷集群</div>
                  <div>大多数缺陷集中在少数模块中（帕累托原则：20%的模块包含80%的缺陷）。</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-content">
              <div class="principle">
                <div class="principle-number">5</div>
                <div class="principle-content">
                  <div class="principle-title">杀虫剂悖论</div>
                  <div>重复执行相同的测试用例会降低发现新缺陷的能力，需要不断更新测试方法。</div>
                </div>
              </div>
              
              <div class="principle">
                <div class="principle-number">6</div>
                <div class="principle-content">
                  <div class="principle-title">测试依赖于上下文</div>
                  <div>不同的系统需要不同的测试方法和侧重点（如医疗系统vs电商系统）。</div>
                </div>
              </div>
              
              <div class="principle">
                <div class="principle-number">7</div>
                <div class="principle-content">
                  <div class="principle-title">没有缺陷的谬误</div>
                  <div>即使软件没有缺陷，如果不满足用户需求和期望，仍然是失败的。</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-pyramid"></i>测试金字塔模型
            </div>
            <div class="chart-container">
              <div class="chart-title">Google测试金字塔</div>
              <canvas id="pyramidChart" width="832" height="375" style="display: block; box-sizing: border-box; height: 250px; width: 554.667px;"></canvas>
            </div>
            <div class="section-content">
              <p>测试金字塔模型由Mike Cohn提出，后被Google等公司广泛采用，指导测试策略的制定和资源分配。</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 测试金字塔图表
      const pyramidCtx = document.getElementById('pyramidChart').getContext('2d');
      const pyramidChart = new Chart(pyramidCtx, {
        type: 'bar',
        data: {
          labels: ['单元测试 (70%)', '集成测试 (20%)', '端到端测试 (10%)'],
          datasets: [{
            axis: 'y',
            data: [70, 20, 10],
            backgroundColor: [
              'rgba(230, 126, 34, 0.8)',
              'rgba(230, 126, 34, 0.6)',
              'rgba(230, 126, 34, 0.4)'
            ],
            borderColor: [
              'rgba(230, 126, 34, 1)',
              'rgba(230, 126, 34, 1)',
              'rgba(230, 126, 34, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          indexAxis: 'y',
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '资源分配比例 (%)',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            y: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return `${context.parsed.x}% 的测试资源`;
                }
              }
            }
          }
        }
      });
    </script>
  



</body></html>