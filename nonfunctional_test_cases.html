<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .design-method {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .method-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .method-content {
        flex: 1;
      }
      .method-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .method-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .example-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 6px;
      }
      .example-table td {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 6px;
        font-size: 14px;
      }
      .example-table tr td:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        font-weight: bold;
      }
      .example-table tr td:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .test-case-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
      }
      .test-case-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        color: #E67E22;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试用例设计实践：非功能测试</div>
        <div class="page-number">9 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tachometer-alt"></i>性能测试用例设计
            </div>
            <div class="section-content">
              <div class="test-case-box">
                <div class="test-case-title">TC-201: 登录页面响应时间测试</div>
                <table class="example-table">
                  <tbody><tr>
                    <td width="30%">测试目标</td>
                    <td>验证登录页面在不同负载下的响应时间</td>
                  </tr>
                  <tr>
                    <td>测试条件</td>
                    <td>并发用户数: 100, 500, 1000</td>
                  </tr>
                  <tr>
                    <td>预期结果</td>
                    <td>页面响应时间 &lt; 2秒 (100用户)<br>页面响应时间 &lt; 5秒 (1000用户)</td>
                  </tr>
                </tbody></table>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-shield-alt"></i>安全测试用例设计
            </div>
            <div class="section-content">
              <div class="test-case-box">
                <div class="test-case-title">TC-301: SQL注入漏洞测试</div>
                <table class="example-table">
                  <tbody><tr>
                    <td width="30%">测试目标</td>
                    <td>验证登录表单是否存在SQL注入漏洞</td>
                  </tr>
                  <tr>
                    <td>测试数据</td>
                    <td>用户名: ' OR 1=1 --<br>密码: 任意值</td>
                  </tr>
                  <tr>
                    <td>预期结果</td>
                    <td>登录失败，显示错误信息，不泄露系统信息</td>
                  </tr>
                </tbody></table>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-universal-access"></i>可用性测试用例设计
            </div>
            <div class="section-content">
              <div class="test-case-box">
                <div class="test-case-title">TC-401: 表单错误提示可用性测试</div>
                <table class="example-table">
                  <tbody><tr>
                    <td width="30%">测试目标</td>
                    <td>验证表单错误提示的可用性和清晰度</td>
                  </tr>
                  <tr>
                    <td>测试步骤</td>
                    <td>1. 在表单中输入无效数据<br>2. 提交表单<br>3. 观察错误提示</td>
                  </tr>
                  <tr>
                    <td>预期结果</td>
                    <td>1. 错误提示靠近相关字段<br>2. 提示信息清晰明确</td>
                  </tr>
                </tbody></table>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-laptop"></i>兼容性测试用例设计
            </div>
            <div class="section-content">
              <div class="test-case-box">
                <div class="test-case-title">TC-501: 跨浏览器兼容性测试</div>
                <table class="example-table">
                  <tbody><tr>
                    <td width="30%">测试目标</td>
                    <td>验证网站在不同浏览器中的兼容性</td>
                  </tr>
                  <tr>
                    <td>测试环境</td>
                    <td>Chrome, Firefox, Safari, Edge最新版本</td>
                  </tr>
                  <tr>
                    <td>测试点</td>
                    <td>1. 页面布局一致性<br>2. 功能正常工作</td>
                  </tr>
                </tbody></table>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
  



</body></html>