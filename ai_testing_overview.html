<html lang="zh"><head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .ai-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .ai-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .ai-content {
        flex: 1;
      }
      .ai-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .ai-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 10px;
        margin: 10px 0;
        border-radius: 0 6px 6px 0;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        height: 200px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .example-box {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
      }
      .example-title {
        font-size: 16px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
      }
      .example-title i {
        margin-right: 8px;
      }
      .example-content {
        font-size: 14px;
        opacity: 0.9;
      }
      .example-result {
        font-size: 14px;
        font-style: italic;
        margin-top: 5px;
        color: #3498DB;
      }
    </style>
  
<style id="manus-iframe-hack-style">
      body {
        overflow: hidden;
        margin: 0;
        padding: 0;
        min-height: 720px;
        min-width: 1280px;
        max-height: none;
        max-width: none;
        height: auto;
        width: auto;
        flex: none;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        align-self: auto;
        justify-self: auto;
        grid-area: auto;
        position: static;
        transform: none;
        box-sizing: border-box;
      }
    </style></head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">AI与智能测试：赋能全流程</div>
        <div class="page-number">13 / 27</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-brain"></i>AI在测试中的核心价值
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">AI测试价值对比</div>
                <canvas id="aiValueChart" width="832" height="255" style="display: block; box-sizing: border-box; height: 170px; width: 554.667px;"></canvas>
              </div>
              
              <div class="highlight-box">
                <strong>AI赋能测试的四大价值：</strong>
                <ul>
                  <li>效率提升：自动化重复性工作，加速测试执行</li>
                  <li>质量提升：发现人工难以识别的模式和缺陷</li>
                  <li>覆盖率提升：生成更全面的测试场景和用例</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-file-alt"></i>AI在需求测试中的应用
            </div>
            <div class="section-content">
              <div class="example-box">
                <div class="example-title">
                  <i class="fas fa-search"></i>需求分析与缺陷预测
                </div>
                <div class="example-content">
                  <strong>实例：</strong> 某金融科技公司使用NLP模型分析需求文档，自动识别模糊、不完整或矛盾的需求描述。
                </div>
                <div class="example-result">
                  结果：需求缺陷发现率提升35%，前期需求澄清效率提高40%
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-sitemap"></i>AI在测试方案设计中的应用
            </div>
            <div class="section-content">
              <div class="example-box">
                <div class="example-title">
                  <i class="fas fa-project-diagram"></i>智能测试策略生成
                </div>
                <div class="example-content">
                  <strong>实例：</strong> 微软使用AI分析代码变更，自动确定最优测试范围和策略，优先执行受影响区域的测试。
                </div>
                <div class="example-result">
                  结果：测试执行时间减少45%，同时保持相同的缺陷发现率
                </div>
              </div>
              
              <div class="ai-box">
                <div class="ai-icon"><i class="fas fa-code-branch"></i></div>
                <div class="ai-content">
                  <div class="ai-title">代码变更影响分析</div>
                  <div class="ai-desc">AI分析代码提交，自动识别受影响的功能模块和测试范围</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tasks"></i>AI在测试用例设计中的应用
            </div>
            <div class="section-content">
              <div class="example-box">
                <div class="example-title">
                  <i class="fas fa-magic"></i>智能测试用例生成
                </div>
                <div class="example-content">
                  <strong>实例：</strong> Google使用AI自动为Android应用生成UI测试用例，基于用户行为模式和应用结构分析。
                </div>
                <div class="example-result">
                  结果：测试覆盖率提升30%，用例设计时间减少60%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // AI测试价值对比图表
      const aiValueCtx = document.getElementById('aiValueChart').getContext('2d');
      const aiValueChart = new Chart(aiValueCtx, {
        type: 'bar',
        data: {
          labels: ['测试效率', '缺陷发现率', '测试覆盖率', '成本节约'],
          datasets: [
            {
              label: '传统测试',
              data: [40, 60, 50, 30],
              backgroundColor: 'rgba(52, 152, 219, 0.7)',
              borderColor: '#3498DB',
              borderWidth: 1
            },
            {
              label: 'AI赋能测试',
              data: [85, 80, 90, 70],
              backgroundColor: 'rgba(230, 126, 34, 0.7)',
              borderColor: '#E67E22',
              borderWidth: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '相对评分',
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#FFFFFF'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  



</body></html>