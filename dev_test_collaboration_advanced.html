<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #2C5F9B 0%, #1E3A5F 100%);
        color: #FFFFFF;
        font-family: 'Arial', sans-serif;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #FFFFFF;
      }
      .page-number {
        font-size: 18px;
        color: #E67E22;
      }
      .content-container {
        display: flex;
        flex: 1;
        gap: 30px;
      }
      .left-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .section {
        margin-bottom: 10px;
      }
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #E67E22;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 10px;
      }
      .section-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .footer {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        text-align: right;
        margin-top: 10px;
      }
      .practice-box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
      }
      .practice-icon {
        color: #E67E22;
        margin-right: 10px;
        margin-top: 3px;
      }
      .practice-content {
        flex: 1;
      }
      .practice-title {
        font-weight: bold;
        margin-bottom: 3px;
      }
      .practice-desc {
        font-size: 14px;
        opacity: 0.9;
      }
      .highlight-box {
        background-color: rgba(230, 126, 34, 0.2);
        border-left: 4px solid #E67E22;
        padding: 8px;
        margin: 8px 0;
        border-radius: 0 6px 6px 0;
        font-size: 14px;
      }
      .chart-container {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        height: 280px;
      }
      .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: center;
      }
      .tool-box {
        background-color: rgba(52, 152, 219, 0.2);
        border-left: 4px solid #3498DB;
        padding: 6px;
        margin: 4px 0;
        border-radius: 0 6px 6px 0;
        font-size: 13px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">
        <div class="title">测试与开发协作：高级实践与技术</div>
        <div class="page-number">26 / 28</div>
      </div>
      
      <div class="content-container">
        <div class="left-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-arrow-left"></i>测试左移：早期质量保障
            </div>
            <div class="section-content">
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-lightbulb"></i></div>
                <div class="practice-content">
                  <div class="practice-title">需求阶段测试介入</div>
                  <div class="practice-desc">测试人员在需求评审阶段就参与，识别潜在缺陷，确保需求的可测试性和完整性。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-pencil-ruler"></i></div>
                <div class="practice-content">
                  <div class="practice-title">设计评审与风险评估</div>
                  <div class="practice-desc">参与架构和设计评审，从测试角度提出建议，预判技术风险点和测试难点。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-file-signature"></i></div>
                <div class="practice-content">
                  <div class="practice-title">验收标准共同制定</div>
                  <div class="practice-desc">与产品、开发共同定义用户故事的验收标准，确保理解一致，减少后期争议。</div>
                </div>
              </div>
              
              <div class="highlight-box">
                <strong>左移效益：</strong> 早期发现缺陷，修复成本降低90%，开发周期缩短30%。
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-arrow-right"></i>测试右移：生产质量监控
            </div>
            <div class="section-content">
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-chart-line"></i></div>
                <div class="practice-content">
                  <div class="practice-title">生产环境监控</div>
                  <div class="practice-desc">测试人员与运维团队协作，建立生产环境监控体系，快速发现和定位问题。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-flask"></i></div>
                <div class="practice-content">
                  <div class="practice-title">混沌工程实践</div>
                  <div class="practice-desc">在生产环境中主动注入故障，验证系统弹性，提升系统稳定性和容错能力。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-sync-alt"></i></div>
                <div class="practice-content">
                  <div class="practice-title">持续反馈闭环</div>
                  <div class="practice-desc">建立从生产到开发、测试的快速反馈机制，持续改进产品质量。</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="right-column">
          <div class="section">
            <div class="section-title">
              <i class="fas fa-tools"></i>工具平台一体化
            </div>
            <div class="section-content">
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-code-branch"></i></div>
                <div class="practice-content">
                  <div class="practice-title">统一代码仓库管理</div>
                  <div class="practice-desc">测试代码与开发代码在同一仓库管理，便于协作和版本追溯，实现代码共享。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-cogs"></i></div>
                <div class="practice-content">
                  <div class="practice-title">CI/CD流水线集成</div>
                  <div class="practice-desc">测试环节深度融入CI/CD流水线，自动化测试与部署同步进行，实现持续质量保障。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-bug"></i></div>
                <div class="practice-content">
                  <div class="practice-title">缺陷管理平台统一</div>
                  <div class="practice-desc">使用统一的缺陷管理平台，确保信息透明、流程规范、责任明确。</div>
                </div>
              </div>
              
              <div class="practice-box">
                <div class="practice-icon"><i class="fas fa-chart-bar"></i></div>
                <div class="practice-content">
                  <div class="practice-title">质量度量指标共享</div>
                  <div class="practice-desc">定期分享测试报告和质量度量指标，共同分析质量趋势，制定改进策略。</div>
                </div>
              </div>
              
              <div class="tool-box">
                <strong>推荐工具：</strong> Git/GitLab (代码协作)、Jenkins/GitLab CI (CI/CD)、Jira/禅道 (缺陷管理)
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">
              <i class="fas fa-rocket"></i>协作成熟度模型
            </div>
            <div class="section-content">
              <div class="chart-container">
                <div class="chart-title">测试开发协作成熟度演进</div>
                <canvas id="maturityChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer">
        研发团队测试培训 | 2025版
      </div>
    </div>
    
    <script>
      // 协作成熟度模型图表
      const maturityCtx = document.getElementById('maturityChart').getContext('2d');
      const maturityChart = new Chart(maturityCtx, {
        type: 'radar',
        data: {
          labels: ['沟通协作', '工具集成', '流程规范', '技能互补', '文化建设', '质量共担'],
          datasets: [{
            label: '初级阶段',
            data: [40, 30, 50, 35, 25, 40],
            backgroundColor: 'rgba(231, 76, 60, 0.2)',
            borderColor: '#E74C3C',
            borderWidth: 2,
            pointBackgroundColor: '#E74C3C',
            pointRadius: 4
          }, {
            label: '成熟阶段',
            data: [90, 85, 95, 80, 85, 90],
            backgroundColor: 'rgba(52, 152, 219, 0.2)',
            borderColor: '#3498DB',
            borderWidth: 2,
            pointBackgroundColor: '#3498DB',
            pointRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              beginAtZero: true,
              max: 100,
              ticks: {
                display: false
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              angleLines: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              pointLabels: {
                color: '#FFFFFF',
                font: {
                  size: 12
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF'
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

